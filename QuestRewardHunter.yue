-- Services and Modules
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Lua module requires (YueScript can directly require Lua modules)
local QuestUtil = require(ReplicatedStorage.Shared.Utils.Stats.QuestUtil)
local GeanieQuestData = require(ReplicatedStorage.Shared.Data.Quests.GenieQuest)
local ItemUtil = require(ReplicatedStorage.Shared.Utils.Stats.ItemUtil)
local Network = require(ReplicatedStorage.Shared.Framework.Network.Remote)
-- local GetRandomWeightedItem = require(ReplicatedStorage.Shared.Framework.Utilities.Math.GetRandomWeightedItem) -- Not used in the original Lua snippet

class _QuestRewardHunterInternal
  -- Properties
  localPlayer: nil
  userData: nil
  rewardHunting: false
  targetReward: nil
  -- seedOffset: 0 -- Seems unused in the logic
  maxRetries: 20
  retryCount: 0
  _allPossibleRewardsList: nil -- Stores the list of all possible rewards
  callbacks: nil -- For event handling

  constructor: =>
    @localPlayer = Players.LocalPlayer
    @_allPossibleRewardsList = {}
    @callbacks = {}
    -- Initialize other state variables
    @rewardHunting = false
    @retryCount = 0
    @maxRetries = 20
    print("QuestRewardHunter initialized") -- Optional: for debugging

  -- Private methods (by convention, prefixed with _)
  _fireCallback: (eventName, ...) =>
    if @callbacks[eventName]
      -- Iterate over a copy in case a callback modifies the list during iteration
      for callback in *@callbacks[eventName]
        task.spawn callback, ...

  _fetchUserData: =>
    -- Attempt to find user data via getgc (use with caution, can be unreliable or slow)
    -- print("Attempting to fetch user data via getgc...") -- Debug log
    for objName, obj in pairs getgc(true) -- Iterate over items from getgc
      if type(obj) == "table" and rawget(obj, "Coins") and rawget(obj, "Gems") and rawget(obj, "GemGenie")
        -- print("User data found via getgc") -- Debug log
        return obj
    
    -- Fallback to LocalData service if getgc fails or doesn't find suitable data
    -- print("User data not found via getgc, falling back to LocalData service...") -- Debug log
    dataModule = require(ReplicatedStorage.Client.Framework.Services.LocalData)
    fetchedData = dataModule:Get()
    -- if fetchedData then print("User data fetched via LocalData service") else print("Failed to fetch user data via LocalData service") -- Debug log
    return fetchedData

  _getFormattedItemName: (item) =>
    name = ItemUtil:GetName item
    amount = ItemUtil:GetAmount item
    
    switch item.Type
      when "Currency"
        "#{amount} #{name}"
      when "Potion"
        levelStr = if item.Level then " Level #{item.Level}" else ""
        "#{amount}x #{name}#{levelStr} Potion"
      when "Powerup"
        "#{amount}x #{name}"
      else
        typeStr = item.Type ? "" -- Use empty string if item.Type is nil
        "#{amount}x #{name} #{typeStr}"

  _getAllRewardsFromSeed: (playerData, seed) =>
    currentRewards = []
    for seedAdd = 0, 2
      questData = GeanieQuestData(playerData, seed + seedAdd)
      if questData and questData.Rewards
        for reward in *questData.Rewards
          hasReward = false
          for existingReward in *currentRewards
            if ItemUtil:Compare(reward, existingReward.data)
              hasReward = true
              break
          unless hasReward
            currentRewards[] = {
              id: ItemUtil:GetName reward,
              amount: ItemUtil:GetAmount reward,
              data: reward
            }
    currentRewards

  _populateAllPossibleRewards: =>
    @_allPossibleRewardsList = [] -- Reset the list
    @userData = @_fetchUserData!
    return unless @userData -- Exit if no user data

    rewardLookup = {}
    -- Optimized loop: iterate fewer times if possible, or use a more representative sample
    -- The original 1 to 1000 with step 10 might be extensive. Consider if a smaller range or dynamic approach is better.
    -- For now, sticking to the original logic's range.
    for currentSeed = 1, 1000, 10 -- Step by 10
      rewardsFromSeed = @_getAllRewardsFromSeed @userData, currentSeed
      for rewardInfo in *rewardsFromSeed
        displayName = @_getFormattedItemName rewardInfo.data
        unless rewardLookup[displayName]
          rewardLookup[displayName] = true
          @_allPossibleRewardsList[] = {
            displayName: displayName,
            data: rewardInfo.data
          }
    
    table.sort @_allPossibleRewardsList, (a, b) -> a.displayName < b.displayName
    -- This method populates the instance variable @_allPossibleRewardsList
    -- No explicit return needed if it's just modifying instance state, 
    -- but can return @_allPossibleRewardsList if chained or used directly.

  _findRewardInCurrentOptions: =>
    return false unless @userData and @targetReward

    baseSeed = @userData.GemGenie.Seed
    for offset = 0, 2
      questData = GeanieQuestData(@userData, baseSeed + offset)
      if questData and questData.Rewards
        for reward in *questData.Rewards
          if ItemUtil:Compare(reward, @targetReward)
            @_fireCallback "rewardFound", offset + 1, reward
            return true
    false

  _rerollQuest: =>
    if @retryCount >= @maxRetries
      @rewardHunting = false
      @_fireCallback "maxRetriesReached"
      return

    Network:FireServer "RerollGenie"
    @retryCount += 1
    @_fireCallback "rerolled", @retryCount
    
    task.delay 0.5, =>
      @userData = @_fetchUserData!
      if @_findRewardInCurrentOptions!
        @rewardHunting = false -- Reward found, stop hunting
      elseif @rewardHunting -- Check if still hunting before recursive call
        @_rerollQuest!

  -- Public API
  getAllPossibleRewards: =>
    @_populateAllPossibleRewards! -- Ensures the list is populated/updated if not already
    @_allPossibleRewardsList -- Returns the populated list (or an empty one if data fetch failed)

  startHunting: (rewardData, maxTriesOverride) =>
    if @rewardHunting
      @stopHunting! -- Stop previous hunt if any

    @userData = @_fetchUserData!
    unless @userData
      @_fireCallback "error", "Failed to fetch user data. Cannot start hunt."
      return false

    @targetReward = rewardData
    @maxRetries = maxTriesOverride ? 20 -- Use override or default to 20
    @retryCount = 0
    @rewardHunting = true
    @_fireCallback "huntStarted", @targetReward

    if @_findRewardInCurrentOptions!
      @rewardHunting = false -- Reward found immediately
      -- @_fireCallback "rewardFound" is already called by _findRewardInCurrentOptions
      return true
    
    @_rerollQuest! -- Start the reroll process
    true

  stopHunting: =>
    if @rewardHunting
      @rewardHunting = false
      @_fireCallback "stopped"
    -- else: not hunting, do nothing or fire a specific event/log

  isHunting: => @rewardHunting

  getRetryCount: => @retryCount

  setMaxRetries: (value) => 
    if type(value) == "number" and value > 0
      @maxRetries = value
    -- else: log an error or ignore invalid input

  -- Event subscription method
  on: (eventName, callbackFn) =>
    @callbacks[eventName] ?= [] -- Initialize if not exists
    @callbacks[eventName][] = callbackFn
    -- Return a function to allow unsubscribing
    return =>
      if @callbacks[eventName]
        for i=#@callbacks[eventName],1,-1 -- Iterate backwards for safe removal
          if @callbacks[eventName][i] == callbackFn
            table.remove @callbacks[eventName], i
            break -- Assuming a callback is only added once

-- Export a singleton instance of the class
export QuestRewardHunter = _QuestRewardHunterInternal!
