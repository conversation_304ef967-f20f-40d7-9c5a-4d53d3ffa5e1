
YueScript
Document
Try yue!
Github
(opens new window)

    Reference
        Introduction
            An Overview of YueScript
        Installation
        Usage
            Lua Module
            YueScript Tool
        Macro
            Common Usage
            Insert Raw Codes
            Export Macro
            Builtin Macro
            Generating Macros with Macros
        Operator
            Chaining Comparisons
            Table Appending
            Table Spreading
            Metatable
            Existence
            Piping
            Nil Coalescing
            Implicit Object
        Module
            Import
            Export
        Assignment
            Perform Update
            Chaining Assignment
            Explicit Locals
            Explicit Globals
        Destructuring Assignment
            Destructuring In Other Places
        If Assignment
            While Assignment
        Varargs Assignment
        Whitespace
            Multiline Chaining
        Comment
        Try
        Attributes
        Literals
            Number Literals
        Function Literals
            Fat Arrows
            Argument Defaults
            Considerations
            Multi-line arguments
        Backcalls
        Table Literals
        Comprehensions
            List Comprehensions
            Table Comprehensions
            Slicing
        For Loop
        Repeat Loop
        While Loop
        Continue
        Conditionals
            In Expression
        Line Decorators
        Switch
            Table Matching
        Object Oriented Programming
            Inheritance
            Super
            Types
            Class Objects
            Class Variables
            Class Declaration Statements
            @ and @@ Values
            Constructor Property Promotion
            Class Expressions
            Anonymous classes
            Class Mixing
        With Statement
        Do
        Function Stubs
        The Using Clause; Controlling Destructive Assignment
        The YueScript Library
            yue
            Config
            Options
        Licence: MIT

#
YueScript
logo
#
Introduction

YueScript is a dynamic language that compiles to Lua. And it's a MoonScript

(opens new window) dialect. The codes written in YueScript are expressive and extremely concise. And it is suitable for writing some changing application logic with more maintainable codes and runs in a Lua embeded environment such as games or website servers.

Yue (月) is the name of moon in Chinese and it's pronounced as [jyɛ].
#
An Overview of YueScript

-- import syntax
import "yue" as :p, :to_lua

-- object literals
inventory =
  equipment:
    * "sword"
    * "shield"
  items:
    * name: "potion"
      count: 10
    * name: "bread"
      count: 3

-- pipe operator
[1, 2, 3]
  |> map (x) -> x * 2
  |> filter (x) -> x > 4
  |> reduce 0, (a, b) -> a + b
  |> print

-- metatable manipulation
apple =
  size: 15
  <index>:
    color: 0x00ffff

with apple
  p .size, .color, .<index> if .<>?

-- js-like export syntax
export 🌛 = "月之脚本"

#
Installation

    Lua Module

 Install luarocks

(opens new window), a package manager for Lua modules. Then install it as a Lua module and executable with:

> luarocks install yuescript

 Or you can build yue.so file with:

> make shared LUAI=/usr/local/include/lua LUAL=/usr/local/lib/lua

 Then get the binary file from path bin/shared/yue.so.

    Build Binary Tool

 Clone this repo, then build and install executable with:

> make install

 Build YueScript tool without macro feature:

> make install NO_MACRO=true

 Build YueScript tool without built-in Lua binary:

> make install NO_LUA=true

    Download Precompiled Binary

 You can download precompiled binary files, including binary executable files compatible with different Lua versions and library files.

 Download precompiled binary files from here

(opens new window).
#
Usage
#
Lua Module

 Use YueScript module in Lua:

    Case 1
    Require "your_yuescript_entry.yue" in Lua.

require("yue")("your_yuescript_entry")

 And this code still works when you compile "your_yuescript_entry.yue" to "your_yuescript_entry.lua" in the same path. In the rest YueScript files just use the normal require or import. The code line numbers in error messages will also be handled correctly.

    Case 2
    Require YueScript module and rewite message by hand.

local yue = require("yue")
yue.insert_loader()
local success, result = xpcall(function()
  return require("yuescript_module_name")
end, function(err)
  return yue.traceback(err)
end)

    Case 3
    Use the YueScript compiler function in Lua.

local yue = require("yue")
local codes, err, globals = yue.to_lua([[
f = ->
  print "hello world"
f!
]],{
  implicit_return_root = true,
  reserve_line_number = true,
  lint_global = true,
  space_over_tab = false,
  options = {
    target = "5.4",
    path = "/script"
  }
})

#
YueScript Tool

 Use YueScript tool with:

> yue -h
Usage: yue [options|files|directories] ...

   -h       Print this message
   -e str   Execute a file or raw codes
   -m       Generate minified codes
   -r       Rewrite output to match original line numbers
   -t path  Specify where to place compiled files
   -o file  Write output to file
   -s       Use spaces in generated codes instead of tabs
   -p       Write output to standard out
   -b       Dump compile time (doesn't write output)
   -g       Dump global variables used in NAME LINE COLUMN
   -l       Write line numbers from source codes
   -j       Disable implicit return at end of file
   -c       Reserve comments before statement from source codes
   -w path  Watch changes and compile every file under directory
   -v       Print version
   --       Read from standard in, print to standard out
            (Must be first and only argument)

   --target=version  Specify the Lua version codes the compiler will generate
                     (version can only be 5.1, 5.2, 5.3 or 5.4)
   --path=path_str   Append an extra Lua search path string to package.path

   Execute without options to enter REPL, type symbol '$'
   in a single line to start/stop multi-line mode

  Use cases:
  Recursively compile every YueScript file with extension .yue under current path: yue .
  Compile and save results to a target path: yue -t /target/path/ .
  Compile and reserve debug info: yue -l .
  Compile and generate minified codes: yue -m .
  Execute raw codes: yue -e 'print 123'
  Execute a YueScript file: yue -e main.yue
#
Macro
#
Common Usage

Macro function is used for evaluating a string in the compile time and insert the generated codes into final compilation.

macro PI2 = -> math.pi * 2
area = $PI2 * 5

macro HELLO = -> "'hello world'"
print $HELLO

macro config = (debugging) ->
  global debugMode = debugging == "true"
  ""

macro asserts = (cond) ->
  debugMode and "assert #{cond}" or ""

macro assert = (cond) ->
  debugMode and "assert #{cond}" or "#{cond}"

$config true
$asserts item ~= nil

$config false
value = $assert item

-- the passed expressions are treated as strings
macro and = (...) -> "#{ table.concat {...}, ' and ' }"
if $and f1!, f2!, f3!
  print "OK"

#
Insert Raw Codes

A macro function can either return a YueScript string or a config table containing Lua codes.

macro yueFunc = (var) -> "local #{var} = ->"
$yueFunc funcA
funcA = -> "fail to assign to the Yue macro defined variable"

macro luaFunc = (var) -> {
  code: "local function #{var}() end"
  type: "lua"
}
$luaFunc funcB
funcB = -> "fail to assign to the Lua macro defined variable"

macro lua = (code) -> {
  :code
  type: "lua"
}

-- the raw string leading and ending symbols are auto trimed
$lua[==[
-- raw Lua codes insertion
if cond then
  print("output")
end
]==]

#
Export Macro

Macro functions can be exported from a module and get imported in another module. You have to put export macro functions in a single file to be used, and only macro definition, macro importing and macro expansion in place can be put into the macro exporting module.

-- file: utils.yue
export macro map = (items, action) -> "[#{action} for _ in *#{items}]"
export macro filter = (items, action) -> "[_ for _ in *#{items} when #{action}]"
export macro foreach = (items, action) -> "for _ in *#{items}
  #{action}"

-- file main.yue
import "utils" as {
  $, -- symbol to import all macros
  $foreach: $each -- rename macro $foreach to $each
}
[1, 2, 3] |> $map(_ * 2) |> $filter(_ > 4) |> $each print _

#
Builtin Macro

There are some builtin macros but you can override them by declaring macros with the same names.

print $FILE -- get string of current module name
print $LINE -- get number 2

#
Generating Macros with Macros

In YueScript, macro functions allow you to generate code at compile time. By nesting macro functions, you can create more complex generation patterns. This feature enables you to define a macro function that generates another macro function, allowing for more dynamic code generation.

macro Enum = (...) ->
	items = {...}
	itemSet = {item, true for item in *items}
	(item) ->
		error "got \"#{item}\", expecting one of #{table.concat items, ', '}" unless itemSet[item]
		"\"#{item}\""

macro BodyType = $Enum(
	Static
	Dynamic
	Kinematic
)

print "Valid enum type:", $BodyType Static
-- print "Compilation error with enum type:", $BodyType Unknown

#
Operator

All of Lua's binary and unary operators are available. Additionally != is as an alias for ~=, and either \ or :: can be used to write a chaining function call like tb\func! or tb::func!. And Yuescipt offers some other special operators to write more expressive codes.

tb\func! if tb ~= nil
tb::func! if tb != nil

#
Chaining Comparisons

Comparisons can be arbitrarily chained:

print 1 < 2 <= 2 < 3 == 3 > 2 >= 1 == 1 < 3 != 5
-- output: true

a = 5
print 1 <= a <= 10
-- output: true

Note the evaluation behavior of chained comparisons:

v = (x) ->
	print x
	x

print v(1) < v(2) <= v(3)
--[[
	output:
	2
	1
	3
	true
]]

print v(1) > v(2) <= v(3)
--[[
	output:
	2
	1
	false
]]

The middle expression is only evaluated once, rather than twice as it would be if the expression were written as v(1) < v(2) and v(2) <= v(3). However, the order of evaluations in a chained comparison is undefined. It is strongly recommended not to use expressions with side effects (such as printing) in chained comparisons. If side effects are required, the short-circuit and operator should be used explicitly.
#
Table Appending

The [] = operator is used to append values to tables.

tab = []
tab[] = "Value"

#
Table Spreading

You can concatenate array tables or hash tables using spread operator ... before expressions in table literals.

parts =
	* "shoulders"
	* "knees"
lyrics =
	* "head"
	* ...parts
	* "and"
	* "toes"

copy = {...other}

a = {1, 2, 3, x: 1}
b = {4, 5, y: 1}
merge = {...a, ...b}

#
Metatable

The <> operator can be used as a shortcut for metatable manipulation.

    Metatable Creation
    Create normal table with empty bracekets <> or metamethod key which is surrounded by <>.

mt = {}
add = (right) => <>: mt, value: @value + right.value
mt.__add = add

a = <>: mt, value: 1
 -- set field with variable of the same name
b = :<add>, value: 2
c = <add>: mt.__add, value: 3

d = a + b + c
print d.value

close _ = <close>: -> print "out of scope"

    Metatable Accessing
    Accessing metatable with <> or metamethod name surrounded by <> or writing some expression in <>.

-- create with metatable containing field "value"
tb = <"value">: 123
tb.<index> = tb.<>
print tb.value

tb.<> = __index: {item: "hello"}
print tb.item

    Metatable Destructure
    Destruct metatable with metamethod key surrounded by <>.

{item, :new, :<close>, <index>: getter} = tb
print item, new, close, getter

#
Existence

The ? operator can be used in a variety of contexts to check for existence.

func?!
print abc?["hello world"]?.xyz

x = tab?.value
len = utf8?.len or string?.len or (o) -> #o

if print and x?
  print x

with? io.open "test.txt", "w"
  \write "hello"
  \close!

#
Piping

Instead of a series of nested function calls, you can pipe values with operator |>.

"hello" |> print
1 |> print 2 -- insert pipe item as the first argument
2 |> print 1, _, 3 -- pipe with a placeholder

-- pipe expression in multiline
readFile "example.txt"
  |> extract language, {}
  |> parse language
  |> emit
  |> render
  |> print

#
Nil Coalescing

The nil-coalescing operator ?? returns the value of its left-hand operand if it isn't nil; otherwise, it evaluates the right-hand operand and returns its result. The ?? operator doesn't evaluate its right-hand operand if the left-hand operand evaluates to non-nil.

local a, b, c, d
a = b ?? c ?? d
func a ?? {}

a ??= false

#
Implicit Object

You can write a list of implicit structures that starts with the symbol * inside a table block. If you are creating implicit object, the fields of the object must be with the same indent.

list =
  * 1
  * 2
  * 3

func
  * 1
  * 2
  * 3

tb =
  name: "abc"

  values:
    * "a"
    * "b"
    * "c"

  objects:
    * name: "a"
      value: 1
      func: => @value + 1
      tb:
        fieldA: 1

    * name: "b"
      value: 2
      func: => @value + 2
      tb: { }

#
Module
#
Import

The import statement is a syntax sugar for requiring a module or help extracting items from an imported module. The imported items are const by default.

-- used as table destructuring
do
  import insert, concat from table
  -- report error when assigning to insert, concat
  import C, Ct, Cmt from require "lpeg"
  -- shortcut for implicit requiring
  import x, y, z from 'mymodule'
  -- import with Python style
  from 'module' import a, b, c

-- shortcut for requring a module
do
  import 'module'
  import 'module_x'
  import "d-a-s-h-e-s"
  import "module.part"

-- requring module with aliasing or table destructuring
do
  import "player" as PlayerModule
  import "lpeg" as :C, :Ct, :Cmt
  import "export" as {one, two, Something:{umm:{ch}}}

#
Export

The export statement offers a concise way to define modules.

    Named Export
    Named export will define a local variable as well as adding a field in the exported table.

export a, b, c = 1, 2, 3
export cool = "cat"

export What = if this
  "abc"
else
  "def"

export y = ->
  hallo = 3434

export class Something
  umm: "cool"

Doing named export with destructuring.

export :loadstring, to_lua: tolua = yue
export {itemA: {:fieldA = 'default'}} = tb

Export named items from module without creating local variables.

export.itemA = tb
export.<index> = items
export["a-b-c"] = 123

    Unnamed Export
    Unnamed export will add the target item into the array part of the exported table.

d, e, f = 3, 2, 1
export d, e, f

export if this
  123
else
  456

export with tmp
  j = 2000

    Default Export
    Using the default keyword in export statement to replace the exported table with any thing.

export default ->
  print "hello"
  123

#
Assignment

The variable is dynamic typed and is defined as local by default. But you can change the scope of declaration by local and global statement.

hello = "world"
a, b, c = 1, 2, 3
hello = 123 -- uses the existing variable

#
Perform Update

You can perform update assignment with many binary operators.

x = 1
x += 1
x -= 1
x *= 10
x /= 10
x %= 10
s ..= "world" -- will add a new local if local variable is not exist
arg or= "default value"

#
Chaining Assignment

You can do chaining assignment to assign multiple items to hold the same value.

a = b = c = d = e = 0
x = y = z = f!

#
Explicit Locals

do
  local a = 1
  local *
  print "forward declare all variables as locals"
  x = -> 1 + y + z
  y, z = 2, 3
  global instance = Item\new!

do
  local X = 1
  local ^
  print "only forward declare upper case variables"
  a = 1
  B = 2

#
Explicit Globals

do
  global a = 1
  global *
  print "declare all variables as globals"
  x = -> 1 + y + z
  y, z = 2, 3

do
  global X = 1
  global ^
  print "only declare upper case variables as globals"
  a = 1
  B = 2
  local Temp = "a local value"

#
Destructuring Assignment

Destructuring assignment is a way to quickly extract values from a table by their name or position in array based tables.

Typically when you see a table literal, {1,2,3}, it is on the right hand side of an assignment because it is a value. Destructuring assignment swaps the role of the table literal, and puts it on the left hand side of an assign statement.

This is best explained with examples. Here is how you would unpack the first two values from a table:

thing = [1, 2]

[a, b] = thing
print a, b

In the destructuring table literal, the key represents the key to read from the right hand side, and the value represents the name the read value will be assigned to.

obj = {
  hello: "world"
  day: "tuesday"
  length: 20
}

{hello: hello, day: the_day} = obj
print hello, the_day

:day = obj -- OK to do simple destructuring without braces

This also works with nested data structures as well:

obj2 = {
  numbers: [1, 2, 3, 4]
  properties: {
    color: "green"
    height: 13.5
  }
}

{numbers: [first, second], properties: {color: color}} = obj2
print first, second, color

If the destructuring statement is complicated, feel free to spread it out over a few lines. A slightly more complicated example:

{
  numbers: [first, second]
  properties: {
    color: color
  }
} = obj2

It’s common to extract values from at table and assign them the local variables that have the same name as the key. In order to avoid repetition we can use the : prefix operator:

{:concat, :insert} = table

This is effectively the same as import, but we can rename fields we want to extract by mixing the syntax:

{:mix, :max, random: rand} = math

You can write default values while doing destructuring like:

{:name = "nameless", :job = "jobless"} = person

You can use _ as placeholder when doing a list destructuring:

[_, two, _, four] = items

#
Destructuring In Other Places

Destructuring can also show up in places where an assignment implicitly takes place. An example of this is a for loop:

tuples = [
  ["hello", "world"]
  ["egg", "head"]
]

for [left, right] in *tuples
  print left, right

We know each element in the array table is a two item tuple, so we can unpack it directly in the names clause of the for statement using a destructure.
#
If Assignment

if and elseif blocks can take an assignment in place of a conditional expression. Upon evaluating the conditional, the assignment will take place and the value that was assigned to will be used as the conditional expression. The assigned variable is only in scope for the body of the conditional, meaning it is never available if the value is not truthy. And you have to use "the walrus operator" := instead of = to do assignment.

if user := database.find_user "moon"
  print user.name

if hello := os.getenv "hello"
  print "You have hello", hello
elseif world := os.getenv "world"
  print "you have world", world
else
  print "nothing :("

If assignment with multiple return values. Only the first value is getting checked, other values are scoped.

if success, result := pcall -> "get result without problems"
  print result -- variable result is scoped
print "OK"

#
While Assignment

You can also use if assignment in a while loop to get the value as the loop condition.

while byte := stream\read_one!
  -- do something with the byte
  print byte

#
Varargs Assignment

You can assign the results returned from a function to a varargs symbol .... And then access its content using the Lua way.

list = [1, 2, 3, 4, 5]
fn = (ok) -> ok, table.unpack list
ok, ... = fn true
count = select '#', ...
first = select 1, ...
print ok, count, first

#
Whitespace

YueScript is a whitespace significant language. You have to write some code block in the same indent with space ' ' or tab '\t' like function body, value list and some control blocks. And expressions containing different whitespaces might mean different things. Tab is treated like 4 space, but it's better not mix the use of spaces and tabs.
#
Multiline Chaining

You can write multi-line chaining function calls with a same indent.

Rx.Observable
  .fromRange 1, 8
  \filter (x) -> x % 2 == 0
  \concat Rx.Observable.of 'who do we appreciate'
  \map (value) -> value .. '!'
  \subscribe print

#
Comment

-- I am a comment

str = --[[
This is a multi-line comment.
It's OK.
]] strA \ -- comment 1
  .. strB \ -- comment 2
  .. strC

func --[[port]] 3000, --[[ip]] "***********"

#
Try

The syntax for Lua error handling in a common form.

try
  func 1, 2, 3
catch err
  print yue.traceback err

success, result = try
  func 1, 2, 3
catch err
  yue.traceback err

try func 1, 2, 3
catch err
  print yue.traceback err

success, result = try func 1, 2, 3

try
  print "trying"
  func 1, 2, 3

-- working with if assignment pattern
if success, result := try func 1, 2, 3
catch err
    print yue.traceback err
  print result

#
Attributes

Syntax support for Lua 5.4 attributes. And you can still use both the const and close declaration and get constant check and scoped callback working when targeting Lua versions below 5.4.

const a = 123
close _ = <close>: -> print "Out of scope."

You can do desctructuring with variables attributed as constant.

const {:a, :b, c, d} = tb
-- a = 1

#
Literals

All of the primitive literals in Lua can be used. This applies to numbers, strings, booleans, and nil.

Unlike Lua, Line breaks are allowed inside of single and double quote strings without an escape sequence:

some_string = "Here is a string
  that has a line break in it."

-- You can mix expressions into string literals using #{} syntax.
-- String interpolation is only available in double quoted strings.
print "I am #{math.random! * 100}% sure."

#
Number Literals

You can use underscores in a number literal to increase readability.

integer = 1_000_000
hex = 0xEF_BB_BF

#
Function Literals

All functions are created using a function expression. A simple function is denoted using the arrow: ->.

my_function = ->
my_function() -- call the empty function

The body of the function can either be one statement placed directly after the arrow, or it can be a series of statements indented on the following lines:

func_a = -> print "hello world"

func_b = ->
  value = 100
  print "The value:", value

If a function has no arguments, it can be called using the ! operator, instead of empty parentheses. The ! invocation is the preferred way to call functions with no arguments.

func_a!
func_b()

Functions with arguments can be created by preceding the arrow with a list of argument names in parentheses:

sum = (x, y) -> print "sum", x + y

Functions can be called by listing the arguments after the name of an expression that evaluates to a function. When chaining together function calls, the arguments are applied to the closest function to the left.

sum 10, 20
print sum 10, 20

a b c "a", "b", "c"

In order to avoid ambiguity in when calling functions, parentheses can also be used to surround the arguments. This is required here in order to make sure the right arguments get sent to the right functions.

print "x:", sum(10, 20), "y:", sum(30, 40)

There must not be any space between the opening parenthesis and the function.

Functions will coerce the last statement in their body into a return statement, this is called implicit return:

sum = (x, y) -> x + y
print "The sum is ", sum 10, 20

And if you need to explicitly return, you can use the return keyword:

sum = (x, y) -> return x + y

Just like in Lua, functions can return multiple values. The last statement must be a list of values separated by commas:

mystery = (x, y) -> x + y, x - y
a, b = mystery 10, 20

#
Fat Arrows

Because it is an idiom in Lua to send an object as the first argument when calling a method, a special syntax is provided for creating functions which automatically includes a self argument.

func = (num) => @value + num

#
Argument Defaults

It is possible to provide default values for the arguments of a function. An argument is determined to be empty if its value is nil. Any nil arguments that have a default value will be replace before the body of the function is run.

my_function = (name = "something", height = 100) ->
  print "Hello I am", name
  print "My height is", height

An argument default value expression is evaluated in the body of the function in the order of the argument declarations. For this reason default values have access to previously declared arguments.

some_args = (x = 100, y = x + 1000) ->
  print x + y

#
Considerations

Because of the expressive parentheses-less way of calling functions, some restrictions must be put in place to avoid parsing ambiguity involving whitespace.

The minus sign plays two roles, a unary negation operator and a binary subtraction operator. Consider how the following examples compile:

a = x - 10
b = x-10
c = x -y
d = x- z

The precedence of the first argument of a function call can be controlled using whitespace if the argument is a literal string. In Lua, it is common to leave off parentheses when calling a function with a single string or table literal.

When there is no space between a variable and a string literal, the function call takes precedence over any following expressions. No other arguments can be passed to the function when it is called this way.

Where there is a space following a variable and a string literal, the function call acts as show above. The string literal belongs to any following expressions (if they exist), which serves as the argument list.

x = func"hello" + 100
y = func "hello" + 100

#
Multi-line arguments

When calling functions that take a large number of arguments, it is convenient to split the argument list over multiple lines. Because of the white-space sensitive nature of the language, care must be taken when splitting up the argument list.

If an argument list is to be continued onto the next line, the current line must end in a comma. And the following line must be indented more than the current indentation. Once indented, all other argument lines must be at the same level of indentation to be part of the argument list

my_func 5, 4, 3,
  8, 9, 10

cool_func 1, 2,
  3, 4,
  5, 6,
  7, 8

This type of invocation can be nested. The level of indentation is used to determine to which function the arguments belong to.

my_func 5, 6, 7,
  6, another_func 6, 7, 8,
    9, 1, 2,
  5, 4

Because tables also use the comma as a delimiter, this indentation syntax is helpful for letting values be part of the argument list instead of being part of the table.

x = [
  1, 2, 3, 4, a_func 4, 5,
    5, 6,
  8, 9, 10
]

Although uncommon, notice how we can give a deeper indentation for function arguments if we know we will be using a lower indentation further on.

y = [ my_func 1, 2, 3,
   4, 5,
  5, 6, 7
]

The same thing can be done with other block level statements like conditionals. We can use indentation level to determine what statement a value belongs to:

if func 1, 2, 3,
  "hello",
  "world"
    print "hello"
    print "I am inside if"

if func 1, 2, 3,
    "hello",
    "world"
  print "hello"
  print "I am inside if"

#
Backcalls

Backcalls are used for unnesting callbacks. They are defined using arrows pointed to the left as the last parameter by default filling in a function call. All the syntax is mostly the same as regular arrow functions except that it is just pointing the other way and the function body does not require indent.

<- f
print "hello"

Fat arrow functions are also available.

<= f
print @value

You can specify a placeholder for where you want the backcall function to go as a parameter.

(x) <- map _, [1, 2, 3]
x * 2

If you wish to have further code after your backcalls, you can set them aside with a do statement.

result, msg = do
  (data) <- readAsync "filename.txt"
  print data
  (info) <- processAsync data
  check info
print result, msg

#
Table Literals

Like in Lua, tables are delimited in curly braces.

some_values = [1, 2, 3, 4]

Unlike Lua, assigning a value to a key in a table is done with : (instead of =).

some_values = {
  name: "Bill",
  age: 200,
  ["favorite food"]: "rice"
}

The curly braces can be left off if a single table of key value pairs is being assigned.

profile =
  height: "4 feet",
  shoe_size: 13,
  favorite_foods: ["ice cream", "donuts"]

Newlines can be used to delimit values instead of a comma (or both):

values = {
  1, 2, 3, 4
  5, 6, 7, 8
  name: "superman"
  occupation: "crime fighting"
}

When creating a single line table literal, the curly braces can also be left off:

my_function dance: "Tango", partner: "none"

y = type: "dog", legs: 4, tails: 1

The keys of a table literal can be language keywords without being escaped:

tbl = {
  do: "something"
  end: "hunger"
}

If you are constructing a table out of variables and wish the keys to be the same as the variable names, then the : prefix operator can be used:

hair = "golden"
height = 200
person = { :hair, :height, shoe_size: 40 }

print_table :hair, :height

If you want the key of a field in the table to to be result of an expression, then you can wrap it in [ ], just like in Lua. You can also use a string literal directly as a key, leaving out the square brackets. This is useful if your key has any special characters.

t = {
  [1 + 2]: "hello"
  "hello world": true
}

Lua tables have both an array part and a hash part, but sometimes you want to make a semantic distinction between array and hash usage when writing Lua tables. Then you can write Lua table with [ ] instead of { } to represent an array table and writing any key value pair in a list table won't be allowed.

some_values = [1, 2, 3, 4]
list_with_one_element = [1, ]

#
Comprehensions

Comprehensions provide a convenient syntax for constructing a new table by iterating over some existing object and applying an expression to its values. There are two kinds of comprehensions: list comprehensions and table comprehensions. They both produce Lua tables; list comprehensions accumulate values into an array-like table, and table comprehensions let you set both the key and the value on each iteration.
#
List Comprehensions

The following creates a copy of the items table but with all the values doubled.

items = [ 1, 2, 3, 4 ]
doubled = [item * 2 for i, item in ipairs items]

The items included in the new table can be restricted with a when clause:

slice = [item for i, item in ipairs items when i > 1 and i < 3]

Because it is common to iterate over the values of a numerically indexed table, an * operator is introduced. The doubled example can be rewritten as:

doubled = [item * 2 for item in *items]

The for and when clauses can be chained as much as desired. The only requirement is that a comprehension has at least one for clause.

Using multiple for clauses is the same as using nested loops:

x_coords = [4, 5, 6, 7]
y_coords = [9, 2, 3]

points = [ [x, y] for x in *x_coords \
for y in *y_coords]

Numeric for loops can also be used in comprehensions:

evens = [i for i = 1, 100 when i % 2 == 0]

#
Table Comprehensions

The syntax for table comprehensions is very similar, only differing by using { and } and taking two values from each iteration.

This example makes a copy of the tablething:

thing = {
  color: "red"
  name: "fast"
  width: 123
}

thing_copy = {k, v for k, v in pairs thing}

no_color = {k, v for k, v in pairs thing when k != "color"}

The * operator is also supported. Here we create a square root look up table for a few numbers.

numbers = [1, 2, 3, 4]
sqrts = {i, math.sqrt i for i in *numbers}

The key-value tuple in a table comprehension can also come from a single expression, in which case the expression should return two values. The first is used as the key and the second is used as the value:

In this example we convert an array of pairs to a table where the first item in the pair is the key and the second is the value.

tuples = [ ["hello", "world"], ["foo", "bar"]]
tbl = {unpack tuple for tuple in *tuples}

#
Slicing

A special syntax is provided to restrict the items that are iterated over when using the * operator. This is equivalent to setting the iteration bounds and a step size in a for loop.

Here we can set the minimum and maximum bounds, taking all items with indexes between 1 and 5 inclusive:

slice = [item for item in *items[1, 5]]

Any of the slice arguments can be left off to use a sensible default. In this example, if the max index is left off it defaults to the length of the table. This will take everything but the first element:

slice = [item for item in *items[2,]]

If the minimum bound is left out, it defaults to 1. Here we only provide a step size and leave the other bounds blank. This takes all odd indexed items: (1, 3, 5, …)

slice = [item for item in *items[,,2]]

#
For Loop

There are two for loop forms, just like in Lua. A numeric one and a generic one:

for i = 10, 20
  print i

for k = 1, 15, 2 -- an optional step provided
  print k

for key, value in pairs object
  print key, value

The slicing and * operators can be used, just like with comprehensions:

for item in *items[2, 4]
  print item

A shorter syntax is also available for all variations when the body is only a single line:

for item in *items do print item

for j = 1, 10, 3 do print j

A for loop can also be used as an expression. The last statement in the body of the for loop is coerced into an expression and appended to an accumulating array table.

Doubling every even number:

doubled_evens = for i = 1, 20
  if i % 2 == 0
    i * 2
  else
    i

You can also filter values by combining the for loop expression with the continue statement.

For loops at the end of a function body are not accumulated into a table for a return value (Instead the function will return nil). Either an explicit return statement can be used, or the loop can be converted into a list comprehension.

func_a = -> for i = 1, 10 do print i
func_b = -> return for i = 1, 10 do i

print func_a! -- prints nil
print func_b! -- prints table object

This is done to avoid the needless creation of tables for functions that don’t need to return the results of the loop.
#
Repeat Loop

The repeat loop comes from Lua:

i = 10
repeat
  print i
  i -= 1
until i == 0

#
While Loop

The while loop also comes in four variations:

i = 10
while i > 0
  print i
  i -= 1

while running == true do my_function!

i = 10
until i == 0
  print i
  i -= 1

until running == false do my_function!

Like for loops, the while loop can also be used an expression. Additionally, for a function to return the accumulated value of a while loop, the statement must be explicitly returned.
#
Continue

A continue statement can be used to skip the current iteration in a loop.

i = 0
while i < 10
  i += 1
  continue if i % 2 == 0
  print i

continue can also be used with loop expressions to prevent that iteration from accumulating into the result. This examples filters the array table into just even numbers:

my_numbers = [1, 2, 3, 4, 5, 6]
odds = for x in *my_numbers
  continue if x % 2 == 1
  x

#
Conditionals

have_coins = false
if have_coins
  print "Got coins"
else
  print "No coins"

A short syntax for single statements can also be used:

have_coins = false
if have_coins then print "Got coins" else print "No coins"

Because if statements can be used as expressions, this can also be written as:

have_coins = false
print if have_coins then "Got coins" else "No coins"

Conditionals can also be used in return statements and assignments:

is_tall = (name) ->
  if name == "Rob"
    true
  else
    false

message = if is_tall "Rob"
  "I am very tall"
else
  "I am not so tall"

print message -- prints: I am very tall

The opposite of if is unless:

unless os.date("%A") == "Monday"
  print "it is not Monday!"

print "You're lucky!" unless math.random! > 0.1

#
In Expression

You can write range checking code with an in-expression.

a = 5

if a in [1, 3, 5, 7]
  print "checking equality with discrete values"

if a in list
  print "checking if `a` is in a list"

print "You're lucky!" unless math.random! > 0.1

#
Line Decorators

For convenience, the for loop and if statement can be applied to single statements at the end of the line:

print "hello world" if name == "Rob"

And with basic loops:

print "item: ", item for item in *items

And with while loops:

game\update! while game\isRunning!

reader\parse_line! until reader\eof!

#
Switch

The switch statement is shorthand for writing a series of if statements that check against the same value. Note that the value is only evaluated once. Like if statements, switches can have an else block to handle no matches. Comparison is done with the == operator.

name = "Dan"
switch name
  when "Robert"
    print "You are Robert"
  when "Dan", "Daniel"
    print "Your name, it's Dan"
  else
    print "I don't know about your name"

A switch when clause can match against multiple values by listing them out comma separated.

Switches can be used as expressions as well, here we can assign the result of the switch to a variable:

b = 1
next_number = switch b
  when 1
    2
  when 2
    3
  else
    error "can't count that high!"

We can use the then keyword to write a switch’s when block on a single line. No extra keyword is needed to write the else block on a single line.

msg = switch math.random(1, 5)
  when 1 then "you are lucky"
  when 2 then "you are almost lucky"
  else "not so lucky"

If you want to write code with one less indent when writing a switch statement, you can put the first when clause on the statement start line, and then all other clauses can be written with one less indent.

switch math.random(1, 5)
  when 1
    print "you are lucky" -- two indents
  else
    print "not so lucky"

switch math.random(1, 5) when 1
  print "you are lucky" -- one indent
else
  print "not so lucky"

It is worth noting the order of the case comparison expression. The case’s expression is on the left hand side. This can be useful if the case’s expression wants to overwrite how the comparison is done by defining an eq metamethod.
#
Table Matching

You can do table matching in a switch when clause, if the table can be destructured by a specific structure and get non-nil values.

items =
  * x: 100
    y: 200
  * width: 300
    height: 400

for item in *items
  switch item
    when :x, :y
      print "Vec2 #{x}, #{y}"
    when :width, :height
      print "size #{width}, #{height}"

You can use default values to optionally destructure the table for some fields.

item = {}

{pos: {:x = 50, :y = 200}} = item -- get error: attempt to index a nil value (field 'pos')

switch item
  when {pos: {:x = 50, :y = 200}}
    print "Vec2 #{x}, #{y}" -- table destructuring will still pass

#
Object Oriented Programming

In these examples, the generated Lua code may appear overwhelming. It is best to focus on the meaning of the YueScript code at first, then look into the Lua code if you wish to know the implementation details.

A simple class:

class Inventory
  new: =>
    @items = {}

  add_item: (name) =>
    if @items[name]
      @items[name] += 1
    else
      @items[name] = 1

A class is declared with a class statement followed by a table-like declaration where all of the methods and properties are listed.

The new property is special in that it will become the constructor.

Notice how all the methods in the class use the fat arrow function syntax. When calling methods on a instance, the instance itself is sent in as the first argument. The fat arrow handles the creation of a self argument.

The @ prefix on a variable name is shorthand for self.. @items becomes self.items.

Creating an instance of the class is done by calling the name of the class as a function.

inv = Inventory!
inv\add_item "t-shirt"
inv\add_item "pants"

Because the instance of the class needs to be sent to the methods when they are called, the \ operator is used.

All properties of a class are shared among the instances. This is fine for functions, but for other types of objects, undesired results may occur.

Consider the example below, the clothes property is shared amongst all instances, so modifications to it in one instance will show up in another:

class Person
  clothes: []
  give_item: (name) =>
    table.insert @clothes, name

a = Person!
b = Person!

a\give_item "pants"
b\give_item "shirt"

-- will print both pants and shirt
print item for item in *a.clothes

The proper way to avoid this problem is to create the mutable state of the object in the constructor:

class Person
  new: =>
    @clothes = []

#
Inheritance

The extends keyword can be used in a class declaration to inherit the properties and methods from another class.

class BackPack extends Inventory
  size: 10
  add_item: (name) =>
    if #@items > size then error "backpack is full"
    super name

Here we extend our Inventory class, and limit the amount of items it can carry.

In this example, we don’t define a constructor on the subclass, so the parent class' constructor is called when we make a new instance. If we did define a constructor then we can use the super method to call the parent constructor.

Whenever a class inherits from another, it sends a message to the parent class by calling the method __inherited on the parent class if it exists. The function receives two arguments, the class that is being inherited and the child class.

class Shelf
  @__inherited: (child) =>
    print @__name, "was inherited by", child.__name

-- will print: Shelf was inherited by Cupboard
class Cupboard extends Shelf

#
Super

super is a special keyword that can be used in two different ways: It can be treated as an object, or it can be called like a function. It only has special functionality when inside a class.

When called as a function, it will call the function of the same name in the parent class. The current self will automatically be passed as the first argument. (As seen in the inheritance example above)

When super is used as a normal value, it is a reference to the parent class object.

It can be accessed like any of object in order to retrieve values in the parent class that might have been shadowed by the child class.

When the \ calling operator is used with super, self is inserted as the first argument instead of the value of super itself. When using . to retrieve a function, the raw function is returned.

A few examples of using super in different ways:

class MyClass extends ParentClass
  a_method: =>
    -- the following have the same effect:
    super "hello", "world"
    super\a_method "hello", "world"
    super.a_method self, "hello", "world"

    -- super as a value is equal to the parent class:
    assert super == ParentClass

super can also be used on left side of a Function Stub. The only major difference is that instead of the resulting function being bound to the value of super, it is bound to self.
#
Types

Every instance of a class carries its type with it. This is stored in the special __class property. This property holds the class object. The class object is what we call to build a new instance. We can also index the class object to retrieve class methods and properties.

b = BackPack!
assert b.__class == BackPack

print BackPack.size -- prints 10

#
Class Objects

The class object is what we create when we use a class statement. The class object is stored in a variable of the same name of the class.

The class object can be called like a function in order to create new instances. That’s how we created instances of classes in the examples above.

A class is made up of two tables. The class table itself, and the base table. The base is used as the metatable for all the instances. All properties listed in the class declaration are placed in the base.

The class object’s metatable reads properties from the base if they don’t exist in the class object. This means we can access functions and properties directly from the class.

It is important to note that assigning to the class object does not assign into the base, so it’s not a valid way to add new methods to instances. Instead the base must explicitly be changed. See the __base field below.

The class object has a couple special properties:

The name of the class as when it was declared is stored as a string in the __name field of the class object.

print BackPack.__name -- prints Backpack

The base object is stored in __base. We can modify this table to add functionality to instances that have already been created and ones that are yet to be created.

If the class extends from anything, the parent class object is stored in __parent.
#
Class Variables

We can create variables directly in the class object instead of in the base by using @ in the front of the property name in a class declaration.

class Things
  @some_func: => print "Hello from", @__name

Things\some_func!

-- class variables not visible in instances
assert Things().some_func == nil

In expressions, we can use @@ to access a value that is stored in the __class of self. Thus, @@hello is shorthand for self.__class.hello.

class Counter
  @count: 0

  new: =>
    @@count += 1

Counter!
Counter!

print Counter.count -- prints 2

The calling semantics of @@ are similar to @. Calling a @@ name will pass the class in as the first argument using Lua’s colon syntax.

@@hello 1,2,3,4

#
Class Declaration Statements

In the body of a class declaration, we can have normal expressions in addition to key/value pairs. In this context, self is equal to the class object.

Here is an alternative way to create a class variable compared to what’s described above:

class Things
  @class_var = "hello world"

These expressions are executed after all the properties have been added to the base.

All variables declared in the body of the class are local to the classes properties. This is convenient for placing private values or helper functions that only the class methods can access:

class MoreThings
  secret = 123
  log = (msg) -> print "LOG:", msg

  some_method: =>
    log "hello world: " .. secret

#
@ and @@ Values

When @ and @@ are prefixed in front of a name they represent, respectively, that name accessed in self and self.__class.

If they are used all by themselves, they are aliases for self and self.__class.

assert @ == self
assert @@ == self.__class

For example, a quick way to create a new instance of the same class from an instance method using @@:

some_instance_method = (...) => @@ ...

#
Constructor Property Promotion

To reduce the boilerplate code for definition of simple value objects. You can write a simple class like:

class Something
  new: (@foo, @bar, @@biz, @@baz) =>

-- Which is short for

class Something
  new: (foo, bar, biz, baz) =>
    @foo = foo
    @bar = bar
    @@biz = biz
    @@baz = baz

You can also use this syntax for a common function to initialize a object's fields.

new = (@fieldA, @fieldB) => @
obj = new {}, 123, "abc"
print obj

#
Class Expressions

The class syntax can also be used as an expression which can be assigned to a variable or explicitly returned.

x = class Bucket
  drops: 0
  add_drop: => @drops += 1

#
Anonymous classes

The name can be left out when declaring a class. The __name attribute will be nil, unless the class expression is in an assignment. The name on the left hand side of the assignment is used instead of nil.

BigBucket = class extends Bucket
  add_drop: => @drops += 10

assert Bucket.__name == "BigBucket"

You can even leave off the body, meaning you can write a blank anonymous class like this:

x = class

#
Class Mixing

You can do mixing with keyword using to copy functions from either a plain table or a predefined class object into your new class. When doing mixing with a plain table, you can override the class indexing function (metamethod __index) to your customized implementation. When doing mixing with an existing class object, the class object's metamethods won't be copied.

MyIndex = __index: var: 1

class X using MyIndex
  func: =>
    print 123

x = X!
print x.var

class Y using X

y = Y!
y\func!

assert y.__class.__parent ~= X -- X is not parent of Y

#
With Statement

A common pattern involving the creation of an object is calling a series of functions and setting a series of properties immediately after creating it.

This results in repeating the name of the object multiple times in code, adding unnecessary noise. A common solution to this is to pass a table in as an argument which contains a collection of keys and values to overwrite. The downside to this is that the constructor of this object must support this form.

The with block helps to alleviate this. Within a with block we can use a special statements that begin with either . or \ which represent those operations applied to the object we are using with on.

For example, we work with a newly created object:

with Person!
  .name = "Oswald"
  \add_relative my_dad
  \save!
  print .name

The with statement can also be used as an expression which returns the value it has been giving access to.

file = with File "favorite_foods.txt"
  \set_encoding "utf8"

Or…

create_person = (name,  relatives) ->
  with Person!
    .name = name
    \add_relative relative for relative in *relatives

me = create_person "Leaf", [dad, mother, sister]

In this usage, with can be seen as a special form of the K combinator.

The expression in the with statement can also be an assignment, if you want to give a name to the expression.

with str = "Hello"
  print "original:", str
  print "upper:", \upper!

Accessing special keys with [] in a with statement.

with tb
  [1] = 1
  print [2]
  with [abc]
    [3] = [2]\func!
    ["key-name"] = value
  [] = "abc" -- appending to "tb"

#
Do

When used as a statement, do works just like it does in Lua.

do
  var = "hello"
  print var
print var -- nil here

YueScript’s do can also be used an expression . Allowing you to combine multiple lines into one. The result of the do expression is the last statement in its body.

counter = do
  i = 0
  ->
    i += 1
    i

print counter!
print counter!

tbl = {
  key: do
    print "assigning key!"
    1234
}

#
Function Stubs

It is common to pass a function from an object around as a value, for example, passing an instance method into a function as a callback. If the function expects the object it is operating on as the first argument then you must somehow bundle that object with the function so it can be called properly.

The function stub syntax is a shorthand for creating a new closure function that bundles both the object and function. This new function calls the wrapped function in the correct context of the object.

Its syntax is the same as calling an instance method with the \ operator but with no argument list provided.

my_object = {
  value: 1000
  write: => print "the value:", @value
}

run_callback = (func) ->
  print "running callback..."
  func!

-- this will not work:
-- the function has to no reference to my_object
run_callback my_object.write

-- function stub syntax
-- lets us bundle the object into a new function
run_callback my_object\write

#
The Using Clause; Controlling Destructive Assignment

While lexical scoping can be a great help in reducing the complexity of the code we write, things can get unwieldy as the code size increases. Consider the following snippet:

i = 100

-- many lines of code...

my_func = ->
  i = 10
  while i > 0
    print i
    i -= 1

my_func!

print i -- will print 0

In my_func, we've overwritten the value of i mistakenly. In this example it is quite obvious, but consider a large, or foreign code base where it isn’t clear what names have already been declared.

It would be helpful to say which variables from the enclosing scope we intend on change, in order to prevent us from changing others by accident.

The using keyword lets us do that. using nil makes sure that no closed variables are overwritten in assignment. The using clause is placed after the argument list in a function, or in place of it if there are no arguments.

i = 100

my_func = (using nil) ->
  i = "hello" -- a new local variable is created here

my_func!
print i -- prints 100, i is unaffected

Multiple names can be separated by commas. Closure values can still be accessed, they just cant be modified:

tmp = 1213
i, k = 100, 50

my_func = (add using k, i) ->
  tmp = tmp + add -- a new local tmp is created
  i += tmp
  k += tmp

my_func(22)
print i, k -- these have been updated

#
The YueScript Library

Access it by require("yue").
#
yue

Description:

The YueScript language library.
#
version

Type: Field.

Description:

The YueScript version.

Signature:

version: string

#
dirsep

Type: Field.

Description:

The file separator for the current platform.

Signature:

dirsep: string

#
yue_compiled

Type: Field.

Description:

The compiled module code cache.

Signature:

yue_compiled: {string: string}

#
to_lua

Type: Function.

Description:

The YueScript compiling function. It compiles the YueScript code to Lua code.

Signature:

to_lua: function(code: string, config?: Config):
		--[[codes]] string | nil,
		--[[error]] string | nil,
		--[[globals]] {{string, integer, integer}} | nil

Parameters:
Parameter 	Type 	Description
code 	string 	The YueScript code.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
string | nil 	The compiled Lua code, or nil if the compilation failed.
string | nil 	The error message, or nil if the compilation succeeded.
| nil 	The global variables appearing in the code (with name, row and column), or nil if the compiler option lint_global is false.
#
file_exist

Type: Function.

Description:

The source file existence checking function. Can be overridden to customize the behavior.

Signature:

file_exist: function(filename: string): boolean

Parameters:
Parameter 	Type 	Description
filename 	string 	The file name.

Returns:
Return Type 	Description
boolean 	Whether the file exists.
#
read_file

Type: Function.

Description:

The source file reading function. Can be overridden to customize the behavior.

Signature:

read_file: function(filename: string): string

Parameters:
Parameter 	Type 	Description
filename 	string 	The file name.

Returns:
Return Type 	Description
string 	The file content.
#
insert_loader

Type: Function.

Description:

Insert the YueScript loader to the package loaders (searchers).

Signature:

insert_loader: function(pos?: integer): boolean

Parameters:
Parameter 	Type 	Description
pos 	integer 	[Optional] The position to insert the loader. Default is 3.

Returns:
Return Type 	Description
boolean 	Whether the loader is inserted successfully. It will fail if the loader is already inserted.
#
remove_loader

Type: Function.

Description:

Remove the YueScript loader from the package loaders (searchers).

Signature:

remove_loader: function(): boolean

Returns:
Return Type 	Description
boolean 	Whether the loader is removed successfully. It will fail if the loader is not inserted.
#
loadstring

Type: Function.

Description:

Loads YueScript code from a string into a function.

Signature:

loadstring: function(input: string, chunkname: string, env: table, config?: Config):
		--[[loaded function]] nil | function(...: any): (any...),
		--[[error]] string | nil

Parameters:
Parameter 	Type 	Description
input 	string 	The YueScript code.
chunkname 	string 	The name of the code chunk.
env 	table 	The environment table.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
function | nil 	The loaded function, or nil if the loading failed.
string | nil 	The error message, or nil if the loading succeeded.
#
loadstring

Type: Function.

Description:

Loads YueScript code from a string into a function.

Signature:

loadstring: function(input: string, chunkname: string, config?: Config):
		--[[loaded function]] nil | function(...: any): (any...),
		--[[error]] string | nil

Parameters:
Parameter 	Type 	Description
input 	string 	The YueScript code.
chunkname 	string 	The name of the code chunk.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
function | nil 	The loaded function, or nil if the loading failed.
string | nil 	The error message, or nil if the loading succeeded.
#
loadstring

Type: Function.

Description:

Loads YueScript code from a string into a function.

Signature:

loadstring: function(input: string, config?: Config):
		--[[loaded function]] nil | function(...: any): (any...),
		--[[error]] string | nil

Parameters:
Parameter 	Type 	Description
input 	string 	The YueScript code.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
function | nil 	The loaded function, or nil if the loading failed.
string | nil 	The error message, or nil if the loading succeeded.
#
loadfile

Type: Function.

Description:

Loads YueScript code from a file into a function.

Signature:

loadfile: function(filename: string, env: table, config?: Config):
		nil | function(...: any): (any...),
		string | nil

Parameters:
Parameter 	Type 	Description
filename 	string 	The file name.
env 	table 	The environment table.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
function | nil 	The loaded function, or nil if the loading failed.
string | nil 	The error message, or nil if the loading succeeded.
#
loadfile

Type: Function.

Description:

Loads YueScript code from a file into a function.

Signature:

loadfile: function(filename: string, config?: Config):
		nil | function(...: any): (any...),
		string | nil

Parameters:
Parameter 	Type 	Description
filename 	string 	The file name.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
function | nil 	The loaded function, or nil if the loading failed.
string | nil 	The error message, or nil if the loading succeeded.
#
dofile

Type: Function.

Description:

Loads YueScript code from a file into a function and executes it.

Signature:

dofile: function(filename: string, env: table, config?: Config): any...

Parameters:
Parameter 	Type 	Description
filename 	string 	The file name.
env 	table 	The environment table.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
any... 	The return values of the loaded function.
#
dofile

Type: Function.

Description:

Loads YueScript code from a file into a function and executes it.

Signature:

dofile: function(filename: string, config?: Config): any...

Parameters:
Parameter 	Type 	Description
filename 	string 	The file name.
config 	Config 	[Optional] The compiler options.

Returns:
Return Type 	Description
any... 	The return values of the loaded function.
#
find_modulepath

Type: Function.

Description:

Resolves the YueScript module name to the file path.

Signature:

find_modulepath: function(name: string): string

Parameters:
Parameter 	Type 	Description
name 	string 	The module name.

Returns:
Return Type 	Description
string 	The file path.
#
pcall

Type: Function.

Description:

Calls a function in protected mode. Catches any errors and returns a status code and results or error object. Rewrites the error line number to the original line number in the YueScript code when errors occur.

Signature:

pcall: function(f: function, ...: any): boolean, any...

Parameters:
Parameter 	Type 	Description
f 	function 	The function to call.
... 	any 	Arguments to pass to the function.

Returns:
Return Type 	Description
boolean, ... 	Status code and function results or error object.
#
require

Type: Function.

Description:

Loads a given module. Can be either a Lua module or a YueScript module. Rewrites the error line number to the original line number in the YueScript code if the module is a YueScript module and loading fails.

Signature:

require: function(name: string): any...

Parameters:
Parameter 	Type 	Description
modname 	string 	The name of the module to load.

Returns:
Return Type 	Description
any 	The value stored at package.loaded[modname] if the module is already loaded.Otherwise, tries to find a loader and returns the final value of package.loaded[modname] and a loader data as a second result.
#
p

Type: Function.

Description:

Inspects the structures of the passed values and prints string representations.

Signature:

p: function(...: any)

Parameters:
Parameter 	Type 	Description
... 	any 	The values to inspect.
#
options

Type: Field.

Description:

The current compiler options.

Signature:

options: Config.Options

#
traceback

Type: Function.

Description:

The traceback function that rewrites the stack trace line numbers to the original line numbers in the YueScript code.

Signature:

traceback: function(message: string): string

Parameters:
Parameter 	Type 	Description
message 	string 	The traceback message.

Returns:
Return Type 	Description
string 	The rewritten traceback message.
#
is_ast

Type: Function.

Description:

Checks whether the code matches the specified AST.

Signature:

is_ast: function(astName: string, code: string): boolean

Parameters:
Parameter 	Type 	Description
astName 	string 	The AST name.
code 	string 	The code.

Returns:
Return Type 	Description
boolean 	Whether the code matches the AST.
#
AST

Type: Field.

Description:

The AST type definition with name, row, column and sub nodes.

Signature:

type AST = {string, integer, integer, any}

#
to_ast

Type: Function.

Description:

Converts the code to the AST.

Signature:

to_ast: function(code: string, flattenLevel?: number, astName?: string):
		--[[AST]] AST | nil,
		--[[error]] nil | string

Parameters:
Parameter 	Type 	Description
code 	string 	The code.
flattenLevel 	integer 	[Optional] The flatten level. Higher level means more flattening. Default is 0. Maximum is 2.
astName 	string 	[Optional] The AST name. Default is "File".

Returns:
Return Type 	Description
AST | nil 	The AST, or nil if the conversion failed.
string | nil 	The error message, or nil if the conversion succeeded.
#
__call

Type: Metamethod.

Description:

Requires the YueScript module. Rewrites the error line number to the original line number in the YueScript code when loading fails.

Signature:

metamethod __call: function(self: yue, module: string): any...

Parameters:
Parameter 	Type 	Description
module 	string 	The module name.

Returns:
Return Type 	Description
any 	The module value.
#
Config

Description:

The compiler compile options.
#
lint_global

Type: Field.

Description:

Whether the compiler should collect the global variables appearing in the code.

Signature:

lint_global: boolean

#
implicit_return_root

Type: Field.

Description:

Whether the compiler should do an implicit return for the root code block.

Signature:

implicit_return_root: boolean

#
reserve_line_number

Type: Field.

Description:

Whether the compiler should reserve the original line number in the compiled code.

Signature:

reserve_line_number: boolean

#
space_over_tab

Type: Field.

Description:

Whether the compiler should use the space character instead of the tab character in the compiled code.

Signature:

space_over_tab: boolean

#
same_module

Type: Field.

Description:

Whether the compiler should treat the code to be compiled as the same currently being compiled module. For internal use only.

Signature:

same_module: boolean

#
line_offset

Type: Field.

Description:

Whether the compiler error message should include the line number offset. For internal use only.

Signature:

line_offset: integer

#
yue.Config.LuaTarget

Type: Enumeration.

Description:

The target Lua version enumeration.

Signature:

enum LuaTarget
	"5.1"
	"5.2"
	"5.3"
	"5.4"
	"5.5"
end

#
options

Type: Field.

Description:

The extra options to be passed to the compilation function.

Signature:

options: Options

#
Options

Description:

The extra compiler options definition.
#
target

Type: Field.

Description:

The target Lua version for the compilation.

Signature:

target: LuaTarget

#
path

Type: Field.

Description:

The extra module search path.

Signature:

path: string

#
dump_locals

Type: Field.

Description:

Whether to dump the local variables in the traceback error message. Default is false.

Signature:

dump_locals: boolean

#
simplified

Type: Field.

Description:

Whether to simplify the error message. Default is true.

Signature:

simplified: boolean

#
Licence: MIT

Copyright (c) 2017-2025 Li Jin <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
