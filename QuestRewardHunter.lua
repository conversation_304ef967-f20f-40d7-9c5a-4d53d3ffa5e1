local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local QuestUtil = require(ReplicatedStorage.Shared.Utils.Stats.QuestUtil)
local GeanieQuestData = require(ReplicatedStorage.Shared.Data.Quests.GenieQuest)
local ItemUtil = require(ReplicatedStorage.Shared.Utils.Stats.ItemUtil)
local Network = require(ReplicatedStorage.Shared.Framework.Network.Remote)
local GetRandomWeightedItem = require(ReplicatedStorage.Shared.Framework.Utilities.Math.GetRandomWeightedItem)

local QuestRewardHunter = {} do
    local localPlayer = Players.LocalPlayer
    local userData = nil
    local rewardHunting = false
    local targetReward = nil
    local seedOffset = 0
    local maxRetries = 20
    local retryCount = 0
    local rewardItems = {}
    local allPossibleRewards = {}
    local callbacks = {}
    
    local function fireCallback(event, ...)
        if callbacks[event] then
            for _, callback in callbacks[event] do
                task.spawn(callback, ...)
            end
        end
    end
    
    local function fetchUserData()
        for _, object in getgc(true) do
            if type(object) == "table" and rawget(object, "Coins") and rawget(object, "Gems") then
                return object
            end
        end
        
        local dataModule = require(ReplicatedStorage.Client.Framework.Services.LocalData)
        return dataModule:Get()
    end
    
    local function getFormattedItemName(item)
        local name = ItemUtil:GetName(item)
        local amount = ItemUtil:GetAmount(item)
        
        if item.Type == "Currency" then
            return amount .. " " .. name
        elseif item.Type == "Potion" then
            local level = item.Level and (" Level " .. item.Level) or ""
            return amount .. "x " .. name .. level .. " Potion"
        elseif item.Type == "Powerup" then
            return amount .. "x " .. name
        else
            return amount .. "x " .. name .. " " .. (item.Type or "")
        end
    end
    
    local function getAllRewardsFromSeed(playerData, seed)
        local allRewards = {}
        
        for seedAdd = 0, 2 do
            local questData = GeanieQuestData(playerData, seed + seedAdd)
            if questData and questData.Rewards then
                for _, reward in questData.Rewards do
                    local rewardId = ItemUtil:GetName(reward)
                    local rewardAmount = ItemUtil:GetAmount(reward)
                    local hasReward = false
                    
                    for i, existingReward in allRewards do
                        if ItemUtil:Compare(reward, existingReward.data) then
                            hasReward = true
                            break
                        end
                    end
                    
                    if not hasReward then
                        table.insert(allRewards, {
                            id = rewardId,
                            amount = rewardAmount,
                            data = reward
                        })
                    end
                end
            end
        end
        
        return allRewards
    end
    
    local function populateAllPossibleRewards()
        allPossibleRewards = {}
        userData = fetchUserData()
        
        if not userData then return end
        
        local rewardLookup = {}
        for seed = 1, 1000, 10 do
            local rewards = getAllRewardsFromSeed(userData, seed)
            for _, reward in rewards do
                local displayName = getFormattedItemName(reward.data)
                if not rewardLookup[displayName] then
                    rewardLookup[displayName] = true
                    table.insert(allPossibleRewards, {
                        displayName = displayName,
                        data = reward.data
                    })
                end
            end
        end
        
        table.sort(allPossibleRewards, function(a, b)
            return a.displayName < b.displayName
        end)
        
        return allPossibleRewards
    end
    
    local function findRewardInCurrentOptions()
        if not userData or not targetReward then return false end

        local baseSeed = userData.GemGenie.Seed
        for offset = 0, 2 do
            local questData = GeanieQuestData(userData, baseSeed + offset)
            if questData and questData.Rewards then
                for _, reward in questData.Rewards do
                    if ItemUtil:Compare(reward, targetReward) then
                        fireCallback("rewardFound", offset + 1, reward)
                        return true
                    end
                end
            end
        end
        
        return false
    end
    
    local function rerollQuest()
        if retryCount >= maxRetries then
            rewardHunting = false
            fireCallback("maxRetriesReached")
            return
        end
        
        Network:FireServer("RerollGenie")
        retryCount = retryCount + 1
        fireCallback("rerolled", retryCount)
        
        task.delay(0.5, function()
            userData = fetchUserData()
            if findRewardInCurrentOptions() then
                rewardHunting = false
            elseif rewardHunting then
                rerollQuest()
            end
        end)
    end
    
    function QuestRewardHunter.getAllPossibleRewards()
        return populateAllPossibleRewards()
    end
    
    function QuestRewardHunter.startHunting(reward, maxTries)
        if rewardHunting then
            QuestRewardHunter.stopHunting()
        end
        
        userData = fetchUserData()
        if not userData then
            fireCallback("error", "Failed to fetch user data")
            return false
        end
        
        targetReward = reward
        maxRetries = maxTries or 20
        retryCount = 0
        rewardHunting = true
        
        if findRewardInCurrentOptions() then
            rewardHunting = false
            return true
        end
        
        rerollQuest()
        return true
    end
    
    function QuestRewardHunter.stopHunting()
        rewardHunting = false
        fireCallback("stopped")
    end
    
    function QuestRewardHunter.isHunting()
        return rewardHunting
    end
    
    function QuestRewardHunter.getRetryCount()
        return retryCount
    end
    
    function QuestRewardHunter.setMaxRetries(value)
        maxRetries = value
    end
    
    function QuestRewardHunter.getItemFromDisplayName(displayName)
        for _, reward in allPossibleRewards do
            if reward.displayName == displayName then
                return reward.data
            end
        end
        return nil
    end
    
    function QuestRewardHunter.addEventListener(event, callback)
        if not callbacks[event] then
            callbacks[event] = {}
        end
        table.insert(callbacks[event], callback)
    end
    
    function QuestRewardHunter.removeEventListener(event, callback)
        if callbacks[event] then
            for i, cb in callbacks[event] do
                if cb == callback then
                    table.remove(callbacks[event], i)
                    break
                end
            end
        end
    end
    
    local function init()
        populateAllPossibleRewards()
    end
    
    task.spawn(init)
end

for i, v in next, QuestRewardHunter.getAllPossibleRewards() do
    print(i, v)
end
