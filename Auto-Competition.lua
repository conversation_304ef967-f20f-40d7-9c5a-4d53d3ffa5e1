-- https://discord.gg/AUmmjk4HAe

local replicatedStorage = game:GetService("ReplicatedStorage")
local players = game:GetService("Players")
local tweenService = game:GetService("TweenService")

local questUtil = require(replicatedStorage.Shared.Utils.Stats.QuestUtil)
local competitiveShared = require(replicatedStorage.Shared.Data.CompetitiveShared)
local localData = require(replicatedStorage.Client.Framework.Services.LocalData)
local itemUtil = require(replicatedStorage.Shared.Utils.Stats.ItemUtil)
local statsUtil = require(replicatedStorage.Shared.Utils.Stats.StatsUtil)

local event = replicatedStorage.Shared.Framework.Network.Remote.Event
local localPlayer = players.LocalPlayer

pcall(function()
	for _, connection in ipairs(getconnections(localPlayer.Idled)) do
		connection:Disable()
	end
end)

local CompetitiveQuestsManager = {} do
    function CompetitiveQuestsManager.getCurrentQuests()
        local playerData = localData:Get()
        if not playerData then return {} end

        local competitiveData = playerData.Competitive
        if not competitiveData then return {} end

        local seasonId = competitiveShared:GetSeason()
        if not seasonId then return {} end

        local quests = {}
        for i = 1, 4 do
            local questId = ("%*-%*"):format(competitiveShared.QuestKey, i)
            local quest = questUtil:FindById(playerData, questId)

            if quest then
                local progress = quest.Progress[1]
                local task = quest.Tasks[1]
                local requirement = questUtil:GetRequirement(task)
                local description = questUtil:FormatTask(task)

                table.insert(quests, {
                    index = i,
                    id = questId,
                    description = description,
                    progress = progress,
                    requirement = requirement,
                    permanent = i <= 2,
                    reward = quest.Rewards[1],
                    task = task
                })
            end
        end

        return quests
    end
end

local PlayerMovement = {} do
    function PlayerMovement.moveToPosition(targetPosition, duration)
        local character = localPlayer.Character
        local humanoid = character and character:FindFirstChildOfClass("Humanoid")
        local rootPart = humanoid and humanoid.RootPart

        if not humanoid or not rootPart then
            return false
        end

        local distance = (rootPart.Position - targetPosition).Magnitude
        if distance < 5 then
            return true
        end

        local tweenInfo = TweenInfo.new(
            duration or 1.0,
            Enum.EasingStyle.Linear,
            Enum.EasingDirection.Out
        )

        local targetCFrame = CFrame.new(targetPosition)
        local currentOrientation = rootPart.CFrame - rootPart.Position
        targetCFrame = targetCFrame * currentOrientation

        local tween = tweenService:Create(rootPart, tweenInfo, { CFrame = targetCFrame })

        local success, result = pcall(function()
            tween:Play()
            tween.Completed:Wait()
        end)

        if not success then
            return false
        end

        task.wait(0.1)
        return true
    end
end

local AutoCompetitive = {} do
    AutoCompetitive.Running = false

    function AutoCompetitive.findEggModel(eggName)
        local workspace = game:GetService("Workspace")
        local rendered = workspace:FindFirstChild("Rendered")
        if not rendered then return nil end

        local eggContainer = rendered:FindFirstChild("Eggs")
        if eggContainer then
           local eggModel = eggContainer:FindFirstChild(eggName)
           if eggModel then return eggModel end
        end

        for _, potentialContainer in ipairs(rendered:GetChildren()) do
             if potentialContainer:IsA("Model") or potentialContainer:IsA("Folder") then
                 local eggModel = potentialContainer:FindFirstChild(eggName)
                 if eggModel and eggModel:FindFirstChild("Root") then
                    return eggModel
                 end
             end
        end

        return nil
    end

    function AutoCompetitive.getMaxHatchAmount()
        local playerData = localData:Get()
        if not playerData then return 1 end
        return statsUtil:GetMaxEggHatches(playerData) or 1
    end

    function AutoCompetitive.handleHatchQuest(quest)
        local eggName = quest.task.Egg
        if not eggName then return false end

        local eggModel = AutoCompetitive.findEggModel(eggName)
        if not eggModel or not eggModel:FindFirstChild("Root") then return false end
        local eggRoot = eggModel.Root

        local rootPart = localPlayer.Character and localPlayer.Character:FindFirstChild("HumanoidRootPart")
        if not rootPart then return false end

        local targetOffset = (rootPart.Position - eggRoot.Position).Unit * 5
        local targetPosition = eggRoot.Position + targetOffset + Vector3.new(0, 0.5, 0)

        local lerpSuccess = PlayerMovement.moveToPosition(targetPosition, 3.0)

        if lerpSuccess then
            local hatchAmount = AutoCompetitive.getMaxHatchAmount()
            event:FireServer("HatchEgg", eggName, hatchAmount)
            task.wait(0.1)
            return true
        end
        return false
    end

    function AutoCompetitive.handleGenericHatchQuest(quest)
         local infinityEgg = AutoCompetitive.findEggModel("Infinity Egg")
         if not infinityEgg or not infinityEgg:FindFirstChild("Root") then return false end
         local eggRoot = infinityEgg.Root

         local rootPart = localPlayer.Character and localPlayer.Character:FindFirstChild("HumanoidRootPart")
         if not rootPart then return false end

         local targetOffset = (rootPart.Position - eggRoot.Position).Unit * 5
         local targetPosition = eggRoot.Position + targetOffset + Vector3.new(0, 0.5, 0)

         local lerpSuccess = PlayerMovement.moveToPosition(targetPosition, 3.0)

         if lerpSuccess then
             local hatchAmount = AutoCompetitive.getMaxHatchAmount()
             event:FireServer("HatchEgg", "Infinity Egg", hatchAmount)
             task.wait(0.1)
             return true
         end
         return false
    end

    function AutoCompetitive.processQuests()
        local quests = CompetitiveQuestsManager.getCurrentQuests()
        if #quests == 0 then return end

        local rerollOccurred = false
        local playerData = localData:Get()
        if not playerData then return end

        local canAffordReroll = itemUtil:GetOwnedAmount(playerData, competitiveShared.RerollCost) >= itemUtil:GetAmount(competitiveShared.RerollCost)

        for _, quest in ipairs(quests) do
            if quest.index == 3 or quest.index == 4 then
                local shouldReroll = false
                if quest.task.Type == "Hatch" and (quest.task.Mythic or quest.task.Shiny) then
                    shouldReroll = true
                end

                if shouldReroll and canAffordReroll then
                    event:FireServer("CompetitiveReroll", quest.index)
                    rerollOccurred = true
                    canAffordReroll = false
                end
            end
        end

        if rerollOccurred then
            task.wait(1.5)
            return
        end

        local specificHatchQuests = {}
        local genericHatchQuests = {}

        for _, quest in ipairs(quests) do
            if quest.task.Type == "Hatch" and quest.progress < quest.requirement then
                if quest.task.Egg then
                    table.insert(specificHatchQuests, quest)
                else
                   table.insert(genericHatchQuests, quest)
                end
            end
        end

        if #specificHatchQuests > 0 then
            table.sort(specificHatchQuests, function(a, b)
                return a.requirement < b.requirement
            end)
            if AutoCompetitive.handleHatchQuest(specificHatchQuests[1]) then
                return
            end
        end

        if #genericHatchQuests > 0 then
             if AutoCompetitive.handleGenericHatchQuest(genericHatchQuests[1]) then
                 return
             end
        end

    end

    function AutoCompetitive.start()
        if AutoCompetitive.Running then return end
        AutoCompetitive.Running = true

        task.spawn(function()
            while AutoCompetitive.Running do
                local success, err = pcall(AutoCompetitive.processQuests)
                if not success then
                    -- Consider adding logging here if needed
                end
                task.wait(1)
            end
        end)
    end

    function AutoCompetitive.stop()
        AutoCompetitive.Running = false
    end
end

AutoCompetitive.start()