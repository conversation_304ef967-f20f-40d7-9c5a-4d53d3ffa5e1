repeat
	task.wait()
until game:IsLoaded()
task.wait(1)

_G.Settings = {
	AutoFarm = false,
	AutoCollect = false,
	AutoTeleport = false,
	AutoClaimChests = false,
	IslandWait = 5,
	RemoveHatchAnim = false,
	ClaimPlaytime = false,
	AutoMysterybox = false,
	AutoDogMinigame = false,
	AutoWheelSpin = false,
	AutoGenieQuest = false,
	AutoClaimSeasonRewards = false,
	PotionSettings = {
		Enabled = false,
		SelectedPotions = {},
		SmartInfinity = true,
	},
	ShopSettings = {
		Enabled = false,
		AlienShop = true,
		ShardShop = true,
		AutoReroll = false,
		RerollShop = "alien-shop",
	},
	RiftSettings = {
		Enabled = false,
		AutoChest = false,
		AutoRoyal = false,
		AutoEgg = false,
		Eggs = {},
		EggOptions = { "x25", "x10", "x5" },
		EggSelected = { "x25", "x10", "x5" },
		KeyOptions = { ["golden-chest"] = "Golden Key", ["royal-chest"] = "Royal Key" },
		HatchAmount = 1,
		MinGoldenKeys = 1,
		MinRoyalKeys = 1,
		_isOpeningGolden = false,
		_isOpeningRoyal = false,
	},
	EnchantSettings = {
		Enabled = false,
		SelectedPet = nil,
		SelectedPetId = nil,
		TargetEnchant = "team-up",
		MinLevel = 3,
		MaxAttempts = 100,
		CurrentAttempts = 0,
		Status = "Idle",
		SelectedSlot = 1,
		UseRerollOrbs = false,
	},
	WebhookSettings = {
		Enabled = false,
		NotifyLegendary = true,
		NotifySecret = true,
		NotifyMythic = true,
		NotifyShiny = true,
		MinimumRarity = "Legendary", -- Common, Rare, Epic, Legendary, Secret, Mythic
		PingEveryone = false,
		ModernStyle = true,
	},

	-- Added Auto Competition Settings
	AutoCompetitionSettings = {
		Enabled = false,
		AutoReroll = true,
		RerollMythic = true,
		RerollNonHatch = true,
		RerollThreshold = 650,
		PriorityOrder = { "RarityHatch", "SpecificHatch", "GenericHatch", "ShinyHatch" },
		EggPreferences = {
			RarityHatch = "Spikey Egg",
			GenericHatch = "Infinity Egg",
			ShinyHatch = "Infinity Egg",
		},
		Delay = 1.0,
		FindEggMethod = "Rendered", -- "Rendered", "Worlds"
		_Running = false,
		_Status = "Idle",
	},
}
local lp = game.Players.LocalPlayer
local tweenService = game:GetService("TweenService")
local replicatedStorage = game:GetService("ReplicatedStorage")
local workspace = game:GetService("Workspace")
local HttpService = game:GetService("HttpService")
local TeleportService = game:GetService("TeleportService")

local datas = require(replicatedStorage.Client.Framework.Services.LocalData)
local storage = require(replicatedStorage.Shared.Data.Gum)
local codesModule = require(replicatedStorage.Shared.Data.Codes)
local rift = require(replicatedStorage.Shared.Data.Rifts)
local egger = require(replicatedStorage.Shared.Data.Eggs)
local statsutil = require(replicatedStorage.Shared.Utils.Stats.StatsUtil)
local enchantsData = require(replicatedStorage.Shared.Data.Enchants)
local playtime = require(game:GetService("ReplicatedStorage").Shared.Data.Playtime)
local shopData = require(replicatedStorage.Shared.Data.Shops)
local shopUtil = require(replicatedStorage.Shared.Utils.ShopUtil)
local itemUtil = require(replicatedStorage.Shared.Utils.Stats.ItemUtil)
local physicalGift = require(game:GetService("ReplicatedStorage").Client.Effects.PhysicalItem.Gift)
local genieQuest = pcall(function() return require(replicatedStorage.Shared.Data.Quests.GenieQuest) end) and require(replicatedStorage.Shared.Data.Quests.GenieQuest) or nil
local seasonUtil = pcall(function() return require(replicatedStorage.Shared.Utils.Stats.SeasonUtil) end) and require(replicatedStorage.Shared.Utils.Stats.SeasonUtil) or nil
local QuestUtil = require(replicatedStorage.Shared.Utils.Stats.QuestUtil)
local CompetitiveShared = require(replicatedStorage.Shared.Data.CompetitiveShared)

local petsData = pcall(function()
	return require(replicatedStorage.Shared.Data.Pets)
end) and require(replicatedStorage.Shared.Data.Pets) or {}
local event = replicatedStorage
	:WaitForChild("Shared")
	:WaitForChild("Framework")
	:WaitForChild("Network")
	:WaitForChild("Remote")
	:WaitForChild("Event")
local remoteFunc = replicatedStorage
	:WaitForChild("Shared")
	:WaitForChild("Framework")
	:WaitForChild("Network")
	:WaitForChild("Remote")
	:WaitForChild("Function")
local network = require(replicatedStorage.Shared.Framework.Network.Remote)

-- Helper functions for remote calls
local function fireEvent(name, ...)
	event:FireServer(name, ...)
end

local function invokeFunc(name, ...)
	return remoteFunc:InvokeServer(name, ...)
end

local function fireNetwork(name, ...)
	network:FireServer(name, ...)
end

local function invokeNetwork(name, ...)
	return network:InvokeServer(name, ...)
end

local webhook = {
	url = "",
	username = "0ne Hub",
}

function webhook.send(content, embeds)
	local success, result = pcall(function()
		if webhook.url == "" then
			print("Webhook URL is empty")
			return false
		end

		local data = {
			content = content,
			username = webhook.username,
			embeds = embeds,
		}

		local requestFunc
		if syn and syn.request then
			requestFunc = syn.request
		elseif http and http.request then
			requestFunc = http.request
		elseif request then
			requestFunc = request
		elseif http_request then
			requestFunc = http_request
		end

		if not requestFunc then
			print("No HTTP request function found")
			return false
		end

		local jsonData
		local jsonSuccess, jsonResult = pcall(function()
			return HttpService:JSONEncode(data)
		end)

		if not jsonSuccess then
			print("JSON encode failed:", jsonResult)
			return false
		end

		jsonData = jsonResult
		if not jsonData then
			print("JSON data is nil after encoding")
			return false
		end

		print("Sending webhook with data length:", #jsonData)

		local reqSuccess, response = pcall(function()
			return requestFunc({
				Url = webhook.url,
				Method = "POST",
				Headers = {
					["Content-Type"] = "application/json",
				},
				Body = jsonData,
			})
		end)

		if not reqSuccess then
			print("Request failed:", response)
			return false
		end

		print("Webhook response status:", response.StatusCode)

		if response.StatusCode ~= 204 and response.StatusCode ~= 200 then
			print("Webhook request failed with status:", response.StatusCode)
			print("Response body:", response.Body)
			return false
		end

		return true
	end)

	if not success then
		print("Webhook error:", result)
	end

	return success and result
end

local data = {}
do
	data.GetData = function()
		return datas.Get()
	end
end

local misc = {}
do
	misc.formatNumber = function(number)
		if not number then
			return "0"
		end

		if number >= 1000000000000 then
			return string.format("%.2fT", number / 1000000000000)
		elseif number >= 1000000000 then
			return string.format("%.2fB", number / 1000000000)
		elseif number >= 1000000 then
			return string.format("%.2fM", number / 1000000)
		elseif number >= 1000 then
			return string.format("%.2fK", number / 1000)
		else
			return tostring(number)
		end
	end

	misc.getHatchAmount = function()
		local playerData = data.GetData()
		if playerData then
			return statsutil:GetMaxEggHatches(playerData)
		end
		return 1
	end
	misc.findChunker = function()
		for _, v in next, workspace.Rendered:GetChildren() do
			local foundModel = v:FindFirstChildWhichIsA("Model")
			if v.Name == "Chunker" and #v:GetChildren() > 0 and foundModel and #foundModel.Name == 36 then
				return v
			end
		end
		return nil
	end

	misc.setupAntiAFK = function()
		pcall(function()
			for _, connection in pairs(getconnections(lp.Idled)) do
				connection:Disable()
			end
		end)
	end
	local function tweenTeleport(position, speed)
		local humanoidRootPart = lp.Character and lp.Character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return
		end

		local distance = (humanoidRootPart.Position - position).magnitude
		local tweenTime = distance / speed

		local tweenInfo = TweenInfo.new(tweenTime, Enum.EasingStyle.Linear, Enum.EasingDirection.Out, 0, false, 0)

		local tween = tweenService:Create(humanoidRootPart, tweenInfo, { CFrame = CFrame.new(position) })

		tween:Play()
		tween.Completed:Wait()
	end
	local function tweenToPos(pos)
		local x = pos.X
		local y = lp.Character.HumanoidRootPart.Position.Y
		local z = pos.Z
		tweenTeleport(Vector3.new(x, y, z), 30)

		tweenTeleport(Vector3.new(x, pos.Y, z), 2000)
	end

	misc.tweenToPos = function(pos)
		pcall(tweenToPos, pos)
	end

	misc.getRiftEgg = function()
		local points = 0
		local bestEgg = nil
		for i, v in next, workspace.Rendered.Rifts:GetChildren() do
			local luck = v:FindFirstChild("Luck", true)
			if luck then
				local foundtoGo = table.find(_G.Settings.RiftSettings.EggSelected, luck.Text)
				local foundWlEgg = table.find(_G.Settings.RiftSettings.Eggs, v.Name)
				if foundtoGo and foundWlEgg then
					local currPoints = 30 / foundtoGo + 100 / foundWlEgg
					if currPoints > points then
						bestEgg = v
						points = currPoints
					end
				end
			end
		end
		return bestEgg
	end
	misc.findInRift = function(egg)
		for i, v in next, rift do
			if v.Egg == egg then
				return i
			end
		end
		return false
	end
	misc.findOutRift = function(egg)
		for i, v in next, rift do
			if i == egg then
				return v.Egg
			end
		end
		return false
	end
	misc.getOrderedEggs = function()
		local eggs = {}
		for i, _ in next, egger do
			if misc.findInRift(i) then
				table.insert(eggs, i)
			end
		end
		table.sort(eggs, function(a, b)
			return egger[a].LayoutOrder > egger[b].LayoutOrder
		end)

		return eggs
	end

	local foundGifts = {}
	pcall(function()
		local sOld = physicalGift.new

		physicalGift.new = function(...)
			local res = sOld(...)
			task.delay(1, table.insert, foundGifts, res)
			return res
		end

		task.spawn(function()
			while true do
				for i, v in next, foundGifts do
					physicalGift.Collect(v)
					table.remove(foundGifts, i)
				end
				task.wait(0.5)
			end
		end)
	end)

	misc.AutoGift = function()
		local gifts = data.GetData().Powerups["Mystery Box"]
		if gifts and gifts > 0 then
			fireEvent("UseGift", "Mystery Box", math.clamp(gifts, 1, 10))
		end
	end

	misc.AutoDoggy = function()
		local claimed = data.GetData().DoggyJump.Claimed
		if claimed < 3 then
			fireEvent("DoggyJumpWin", 3)
		end
	end

	misc.AutoWheelSpin = function()
		local playerData = data.GetData()
		if not playerData then
			return
		end

		local currentTime = workspace:GetServerTimeNow()
		local nextWheelSpin = playerData.NextWheelSpin or 0

		if currentTime >= nextWheelSpin then
			fireNetwork("ClaimFreeWheelSpin")
			Library:Notify({
				Title = "Wheel Spin",
				Description = "Claimed free wheel spin!",
				Time = 3,
			})
			task.wait(0.5)
		end

		local spinTickets = itemUtil:GetOwnedAmount(playerData, {
			["Type"] = "Powerup",
			["Name"] = "Spin Ticket",
		})

		if spinTickets > 0 then
			local result = invokeNetwork("WheelSpin")
			if result then
				Library:Notify({
					Title = "Wheel Spin",
					Description = "Used a spin ticket!",
					Time = 3,
				})
				task.wait(0.5)
				fireNetwork("ClaimWheelSpinQueue")
			end
		end
	end

	misc.AutoGenieQuest = function()
		local playerData = data.GetData()
		if not playerData then
			return
		end

		-- Check if there's an active quest
		local activeQuest = playerData.Quests and playerData.Quests["gem-genie"]

		-- Check if we can get a new quest (timer expired)
		local currentTime = workspace:GetServerTimeNow()
		local nextGenieTime = playerData.GemGenie and playerData.GemGenie.Next or 0
		local canGetNewQuest = currentTime >= nextGenieTime

		if activeQuest then
			-- We already have an active quest, nothing to do
			return
		end

		if canGetNewQuest then
			-- Get quest options and evaluate them
			local questSeed = playerData.GemGenie and playerData.GemGenie.Seed or 0
			local bestOption = 1
			local bestValue = 0

			-- Try to evaluate all 3 quest options
			for i = 1, 3 do
				local questValue = 0

				-- Attempt to get quest data
				pcall(function()
					-- Use the pre-loaded genieQuest module
					if genieQuest then
						local questData = genieQuest(playerData, questSeed + (i - 1))

						if questData and questData.Reward then
							local reward = questData.Reward

							-- Evaluate reward value
							if reward.Type == "Currency" then
								if reward.Currency == "Gems" then
									questValue = reward.Amount * 10 -- Gems are valuable
								elseif reward.Currency == "Coins" then
									questValue = reward.Amount / 1000 -- Coins are less valuable
								elseif reward.Currency == "Shards" then
									questValue = reward.Amount * 5 -- Shards are moderately valuable
								end
							elseif reward.Type == "Powerup" then
								-- Powerups like reroll orbs, spin tickets, etc.
								if reward.Name == "Reroll Orb" then
									questValue = reward.Amount * 500
								elseif reward.Name == "Spin Ticket" then
									questValue = reward.Amount * 300
								else
									questValue = reward.Amount * 100
								end
							elseif reward.Type == "Pet" then
								-- Pets are very valuable
								local rarityValue = {
									Common = 100,
									Rare = 500,
									Epic = 1000,
									Legendary = 5000,
									Secret = 10000,
									Mythic = 20000
								}
								questValue = rarityValue[reward.Rarity] or 1000
							end
						end
					end
				end)

				-- Update best option if this one is better
				if questValue > bestValue then
					bestValue = questValue
					bestOption = i
				end
			end

			-- Start the best quest option
			fireNetwork("StartGenieQuest", bestOption)
			Library:Notify({
				Title = "Gem Genie",
				Description = "Started a new Gem Genie quest (option " .. bestOption .. ")!",
				Time = 3,
			})
		end
	end
end

local main = {}
do
	main.Sell = function()
		fireEvent("SellBubble")
	end
	main.Blow = function()
		fireEvent("BlowBubble")
	end

	main.BlowAndSell = function()
		local plrData = data.GetData()
		if plrData.Bubble.Amount >= storage[plrData.Bubble.Gum].Storage then
			main.Sell()
		end

		main.Blow()
	end

	main.UseCodes = function()
		for i, _ in next, codesModule do
			if not table.find(data.GetData().Redeemed, i) then
				invokeFunc("RedeemCode", i)
			end
		end
	end
	local collectPickup =
		replicatedStorage:WaitForChild("Remotes"):WaitForChild("Pickups"):WaitForChild("CollectPickup")

	main.alreadyCollected = { t = tick(), items = {} }
	local function pickUper()
		local chunker = misc.findChunker()
		if (tick() - main.alreadyCollected.t) > 300 then
			main.alreadyCollected.t = tick()
			main.alreadyCollected.items = {}
		end
		if chunker then
			for _, v in next, chunker:GetChildren() do
				if main.alreadyCollected.items[v.Name] then
					continue
				end
				collectPickup:FireServer(v.Name)
				table.insert(main.alreadyCollected.items, v)
				v:Remove()
			end
		end
	end
	main.CollectPickup = function()
		pcall(pickUper)
	end

	main.UnlockIslands = function()
		if firetouchinterest then
			for _ = 1, 10 do
				for _, v in next, workspace.Worlds["The Overworld"].Islands:GetChildren() do
					firetouchinterest(v.Island.UnlockHitbox, lp.Character.HumanoidRootPart, 0)
					firetouchinterest(v.Island.UnlockHitbox, lp.Character.HumanoidRootPart, 1)
					firetouchinterest(v.Island.UnlockHitbox, lp.Character.HumanoidRootPart, 0)
				end
				task.wait(0.2)
			end
		end
	end

	main.ClaimChests = function()
		for _, v in next, workspace.Rendered.Chests:GetChildren() do
			fireEvent("ClaimChest", v.Name, true)
		end
	end

	main.TeleportToIslands = function(tWait)
		for _, v in next, workspace.Worlds["The Overworld"].Islands:GetChildren() do
			fireEvent("Teleport", "Workspace.Worlds.The Overworld.Islands." .. v.Name .. ".Island.Portal.Spawn")
			task.wait(tWait)
		end
	end

	main.DoRifts = function()
		local playerdata = data.GetData()
		if not playerdata then return false end

		local foundgoldenchest, foundroyalchest = false, false

		for _, v in next, workspace.Rendered.Rifts:GetChildren() do
			if not v.Name:find("chest") then continue end

			local keytype = _G.Settings.RiftSettings.KeyOptions[v.Name]
			local keycount = playerdata.Powerups[keytype] or 0
			if keycount <= 0 then continue end

			if v.Name == "golden-chest" then
				foundgoldenchest = true
				if not (_G.Settings.RiftSettings.AutoChest and (_G.Settings.RiftSettings._isOpeningGolden or keycount >= _G.Settings.RiftSettings.MinGoldenKeys)) then continue end
				_G.Settings.RiftSettings._isOpeningGolden = true
			elseif v.Name == "royal-chest" then
				foundroyalchest = true
				if not (_G.Settings.RiftSettings.AutoRoyal and (_G.Settings.RiftSettings._isOpeningRoyal or keycount >= _G.Settings.RiftSettings.MinRoyalKeys)) then continue end
				_G.Settings.RiftSettings._isOpeningRoyal = true
			else
				continue
			end

			if (v.Output.Position - lp.Character.HumanoidRootPart.Position).magnitude > 20 then
				misc.tweenToPos(v.Output.Position + Vector3.new(0, 15, 0))
			else
				fireEvent("UnlockRiftChest", v.Name)
			end
			return true
		end

		if not foundgoldenchest then _G.Settings.RiftSettings._isOpeningGolden = false end
		if not foundroyalchest then _G.Settings.RiftSettings._isOpeningRoyal = false end

		if not _G.Settings.RiftSettings.AutoEgg then return false end

		local gotegg = misc.getRiftEgg()
		if not gotegg then return false end

		if (gotegg.Output.Position - lp.Character.HumanoidRootPart.Position).magnitude > 20 then
			misc.tweenToPos(gotegg.Output.Position + Vector3.new(0, 15, 0))
		else
			fireEvent("HatchEgg", rift[gotegg.Name].Egg, misc.getHatchAmount())
		end
		return true
	end
end

local enchant = {}
do
	enchant.GetEquippedPets = function()
		local playerData = data.GetData()
		local equippedPetsInfo = {}
		local equippedPetsDropdown = {}

		if not playerData or not playerData.Pets then
			return {}, {}
		end
		local activeTeamIndex = playerData.TeamEquipped or 1

		local activeTeam = playerData.Teams and playerData.Teams[activeTeamIndex]
		if not activeTeam or not activeTeam.Pets then
			return {}, {}
		end

		local equippedPetIds = activeTeam.Pets

		for _, petId in pairs(equippedPetIds) do
			for _, pet in pairs(playerData.Pets) do
				if pet.Id == petId then
					local enchantInfo = ""
					if pet.Enchants then
						for slotIdx, enchant in pairs(pet.Enchants) do
							if enchantInfo ~= "" then
								enchantInfo = enchantInfo .. ", "
							end
							enchantInfo = enchantInfo
								.. "Slot "
								.. slotIdx
								.. ": "
								.. enchant.Id
								.. " "
								.. enchant.Level
						end
					end

					if enchantInfo == "" then
						enchantInfo = "No enchants"
					end

					local displayName = pet.Name .. " - " .. enchantInfo
					table.insert(equippedPetsDropdown, displayName)
					equippedPetsInfo[displayName] = pet
					break
				end
			end
		end

		return equippedPetsInfo, equippedPetsDropdown
	end

	enchant.GetAvailableEnchants = function()
		local enchantsList = {}

		for enchantId, enchantInfo in pairs(enchantsData) do
			table.insert(enchantsList, enchantId)
		end

		return enchantsList
	end

	enchant.GetPetEnchants = function(petId)
		local playerData = data.GetData()
		if not playerData or not playerData.Pets then
			return nil
		end

		for _, pet in pairs(playerData.Pets) do
			if pet.Id == petId then
				return pet.Enchants
			end
		end

		return nil
	end

	enchant.RerollEnchants = function(petId)
		local newPetId = invokeNetwork("RerollEnchants", petId)
		return newPetId
	end

	enchant.RerollEnchantSlot = function(petId, slotIndex)
		local playerData = data.GetData()
		if not playerData then
			return nil
		end

		local rerollCost = {
			["Type"] = "Powerup",
			["Name"] = "Reroll Orb",
			["Amount"] = 1,
		}

		if _G.Settings.EnchantSettings.UseRerollOrbs and itemUtil:CanAfford(playerData, rerollCost) then
			fireNetwork("RerollEnchant", petId, slotIndex)
			return petId
		else
			return enchant.RerollEnchants(petId)
		end
	end

	enchant.HasDesiredEnchant = function(petId, enchantId, minLevel, slotIndex)
		local enchants = enchant.GetPetEnchants(petId)
		if not enchants then
			return false
		end

		-- When using reroll orbs, we only check the specific slot being rerolled
		-- When not using reroll orbs (using gems), we check all slots since all slots get rerolled
		if _G.Settings.EnchantSettings.UseRerollOrbs then
			-- Only check the specific slot when using orbs
			if slotIndex and enchants[slotIndex] then
				return enchants[slotIndex].Id == enchantId and enchants[slotIndex].Level >= minLevel
			end
		else
			-- When using gems to reroll, check all slots
			for _, v in pairs(enchants) do
				if v.Id == enchantId and v.Level >= minLevel then
					return true
				end
			end
		end

		return false
	end

	enchant.AutoEnchant = function()
		_G.Settings.EnchantSettings.CurrentAttempts = 0
		_G.Settings.EnchantSettings.Status = "Starting..."

		while _G.Settings.EnchantSettings.Enabled do
			local petId = _G.Settings.EnchantSettings.SelectedPetId
			local targetEnchant = _G.Settings.EnchantSettings.TargetEnchant
			local minLevel = _G.Settings.EnchantSettings.MinLevel
			local slotIndex = _G.Settings.EnchantSettings.SelectedSlot

			if not petId then
				_G.Settings.EnchantSettings.Status = "No pet selected"
				break
			end

			if enchant.HasDesiredEnchant(petId, targetEnchant, minLevel, slotIndex) then
				_G.Settings.EnchantSettings.Status = "Success! Found "
					.. targetEnchant
					.. " level "
					.. minLevel
					.. "+ in slot "
					.. slotIndex
				_G.Settings.EnchantSettings.Enabled = false
				break
			end

			if _G.Settings.EnchantSettings.CurrentAttempts >= _G.Settings.EnchantSettings.MaxAttempts then
				_G.Settings.EnchantSettings.Status = "Max attempts reached ("
					.. _G.Settings.EnchantSettings.MaxAttempts
					.. ")"
				_G.Settings.EnchantSettings.Enabled = false
				break
			end

			_G.Settings.EnchantSettings.Status = "Rerolling slot "
				.. slotIndex
				.. "... Attempt: "
				.. _G.Settings.EnchantSettings.CurrentAttempts + 1

			local newPetId
			if _G.Settings.EnchantSettings.UseRerollOrbs then
				newPetId = enchant.RerollEnchantSlot(petId, slotIndex)
			else
				newPetId = enchant.RerollEnchants(petId)
			end

			if newPetId then
				_G.Settings.EnchantSettings.SelectedPetId = newPetId
				_G.Settings.EnchantSettings.CurrentAttempts = _G.Settings.EnchantSettings.CurrentAttempts + 1
			else
				_G.Settings.EnchantSettings.Status = "Error rerolling enchants"
				_G.Settings.EnchantSettings.Enabled = false
				break
			end

			task.wait(0.3)
		end
	end
end

local potion = {}
do
	local potionsModule = require(game:GetService("ReplicatedStorage").Shared.Data.Potions)

	potion.GetAllPotions = function()
		local potionsModule

		local success = pcall(function()
			potionsModule = require(game:GetService("ReplicatedStorage").Shared.Data.Potions)
		end)

		if not success or not potionsModule then
			return {}
		end

		local potionsWithLevels = {}

		for potionName, potionData in pairs(potionsModule) do
			if potionData.OneLevel then
				table.insert(potionsWithLevels, {
					Name = potionName,
					Level = 1,
					DisplayName = potionName,
				})
			else
				for level = 1, 6 do
					local displayName
					if level == 6 then
						displayName = potionName .. " Evolved"
					else
						displayName = potionName .. " (Level " .. level .. ")"
					end

					table.insert(potionsWithLevels, {
						Name = potionName,
						Level = level,
						DisplayName = displayName,
					})
				end
			end
		end

		table.sort(potionsWithLevels, function(a, b)
			if a.Name == b.Name then
				return a.Level > b.Level
			end
			return a.Name < b.Name
		end)

		return potionsWithLevels
	end

	potion.GetPlayerPotions = function()
		local playerData = data.GetData()
		if not playerData or not playerData.Potions then
			return {}
		end

		local playerPotions = {}

		for _, potionData in pairs(playerData.Potions) do
			local potionName = potionData.Name
			local potionLevel = potionData.Level or 1
			local amount = potionData.Amount or 1

			table.insert(playerPotions, {
				Name = potionName,
				Level = potionLevel,
				Amount = amount,
				DisplayName = potionLevel == 6 and (potionName .. " Evolved")
					or (potionName .. " (Level " .. potionLevel .. ")"),
			})
		end

		return playerPotions
	end

	potion.GetActivePotions = function()
		local playerData = data.GetData()
		if not playerData or not playerData.ActivePotions then
			return {}
		end

		local activePotions = {}

		for potionName, potionData in pairs(playerData.ActivePotions) do
			if potionData.Active then
				local level = potionData.Active.Level or 1
				local timeLeft = potionData.Active.TimeLeft or 0

				table.insert(activePotions, {
					Name = potionName,
					Level = level,
					TimeLeft = timeLeft,
					DisplayName = level == 6 and (potionName .. " Evolved")
						or (potionName .. " (Level " .. level .. ")"),
				})
			end
		end

		return activePotions
	end

	potion.IsPotionActive = function(potionName)
		local activePotions = potion.GetActivePotions()

		for _, activePotionData in pairs(activePotions) do
			if activePotionData.Name == potionName then
				return true, activePotionData.Level, activePotionData.TimeLeft
			end
		end

		return false, 0, 0
	end

	potion.GetBestPlayerPotion = function(potionName)
		local playerPotions = potion.GetPlayerPotions()
		local bestLevel = 0
		local bestPotion = nil

		for _, potionData in pairs(playerPotions) do
			if potionData.Name == potionName and potionData.Level > bestLevel then
				bestLevel = potionData.Level
				bestPotion = potionData
			end
		end

		return bestPotion
	end

	potion.UsePotion = function(potionName, potionLevel)
		fireNetwork("UsePotion", potionName, potionLevel)

		local description
		if potionLevel == 6 then
			description = "Used " .. potionName .. " Evolved"
		else
			description = "Used " .. potionName .. " (Level " .. potionLevel .. ")"
		end

		Library:Notify({
			Title = "Auto Potion",
			Description = description,
			Time = 3,
		})
		return true
	end


	potion.ParsePotionName = function(potionParts)
		if potionParts[#potionParts] == "Evolved" then
			return table.concat(potionParts, " ", 1, #potionParts - 1), 6
		elseif potionParts[#potionParts - 1] == "(Level" and potionParts[#potionParts]:sub(-1) == ")" then
			return table.concat(potionParts, " ", 1, #potionParts - 2), tonumber(potionParts[#potionParts]:sub(1, -2))
		else
			return table.concat(potionParts, " "), 1
		end
	end

	potion.AreBothEvolvedPotionsActive = function(activePotions)
		local luckyPotion = activePotions["Lucky"]
		local speedPotion = activePotions["Speed"]
		return luckyPotion and speedPotion and luckyPotion.Level == 6 and speedPotion.Level == 6
	end

	potion.AutoUsePotion = function()
		local lastActivePotions = {}

		while true do
			if not _G.Settings.PotionSettings.Enabled then
				return
			end

			task.wait(0.2)

			local activePotions = {}
			local activeData = potion.GetActivePotions()
			for _, potionData in pairs(activeData) do
				activePotions[potionData.Name] = potionData
			end

			local potionExpired = false
			for potionName, _ in pairs(lastActivePotions) do
				if not activePotions[potionName] then
					potionExpired = true
					break
				end
			end

			lastActivePotions = activePotions

			if _G.Settings.PotionSettings.SmartInfinity then
				local infinityActive = activePotions["Infinity Elixir"] ~= nil
				local luckyPotion = activePotions["Lucky"]
				local speedPotion = activePotions["Speed"]
				local bothEvolvedActive = luckyPotion and speedPotion and luckyPotion.Level == 6 and speedPotion.Level == 6

				if not infinityActive and bothEvolvedActive then
					local infinityPotion = potion.GetBestPlayerPotion("Infinity Elixir")
					if infinityPotion then
						Library:Notify({
							Title = "Smart Infinity",
							Description = "Both Lucky Evolved and Speed Evolved are active. Using Infinity Elixir.",
							Time = 3,
						})
						potion.UsePotion(infinityPotion.Name, infinityPotion.Level)
						task.wait(1)
						continue
					end
				end
			end

			for _, potionKey in pairs(_G.Settings.PotionSettings.SelectedPotions) do
				local potionParts = string.split(potionKey, " ")
				local potionName, potionLevel = potion.ParsePotionName(potionParts)

				if potionName == "Infinity Elixir" and _G.Settings.PotionSettings.SmartInfinity then
					if not potion.AreBothEvolvedPotionsActive(activePotions) then
						continue
					end
				end

				local isActive, activeLevel = potion.IsPotionActive(potionName)

				if isActive and potionLevel <= activeLevel and not potionExpired then
					continue
				end

				local playerPotion = potion.GetBestPlayerPotion(potionName)
				if not playerPotion then
					continue
				end

				if not isActive or playerPotion.Level > activeLevel then
					potion.UsePotion(playerPotion.Name, playerPotion.Level)
					task.wait(1)
					break
				end
			end
		end
	end
end

local rewardClaim = {}
do
	rewardClaim.PlaytimeRewards = function()
		local playRewards = data.GetData().PlaytimeRewards
		if not playRewards then return end

		local currTime = workspace:GetServerTimeNow()
		local start = playRewards.Start
		local timePassed = currTime - start

		for i, v in next, playtime.Gifts do
			if timePassed < v.Time then continue end
			if playRewards.Claimed[tostring(i)] then continue end

			invokeFunc("ClaimPlaytime", tonumber(i))
		end
	end

	rewardClaim.SeasonRewards = function()
		if not seasonUtil then return end

		local playerData = data.GetData()
		if not playerData or not playerData.Season then return end

		local currentSeason = seasonUtil:GetCurrentSeason()
		if not currentSeason then return end

		local canClaim = false
		local level = playerData.Season.Level

		if playerData.Season.IsInfinite then
			canClaim = true
		else
			if level > 0 and level <= #currentSeason.Track then
				canClaim = true
			end
		end

		if canClaim then
			local success = pcall(function()
				fireNetwork("ClaimSeason")
			end)

			if success then
				Library:Notify({
					Title = "Season Rewards",
					Description = "Claimed season rewards for level " .. level,
					Time = 3,
				})
			end

			return success
		end

		return false
	end
end

local shop = {}
do
	shop.GetShopItems = function(shopId)
		local playerData = data.GetData()
		if not playerData then
			return {}, {}, {}
		end

		local items, stocks, sales = shopUtil:GetItemsData(shopId, lp, playerData)
		return items, stocks, sales
	end

	shop.CanBuyItems = function(shopId)
		local items, stocks, _ = shop.GetShopItems(shopId)
		local playerData = data.GetData()
		if not playerData then
			return false
		end

		local shopInfo = playerData.Shops[shopId]
		if not shopInfo then
			return false
		end

		local unlockedSlots = shopUtil:GetUnlockedSlots(playerData, #items)

		for i, item in pairs(items) do
			if i <= unlockedSlots then
				local bought = shopInfo.Bought[i] or 0
				local stock = stocks[i] or 0
				local remaining = stock - bought

				if remaining > 0 and itemUtil:CanAfford(playerData, item.Cost) then
					return true
				end
			end
		end

		return false
	end

	shop.BuyBestItems = function(shopId)
		local items, stocks, _ = shop.GetShopItems(shopId)
		local playerData = data.GetData()
		if not playerData then
			return false
		end

		local shopInfo = playerData.Shops[shopId]
		if not shopInfo then
			return false
		end

		local unlockedSlots = shopUtil:GetUnlockedSlots(playerData, #items)

		local sortedItems = {}
		for i, item in pairs(items) do
			if i <= unlockedSlots then
				local bought = shopInfo.Bought[i] or 0
				local stock = stocks[i] or 0
				local remaining = stock - bought

				if remaining > 0 and itemUtil:CanAfford(playerData, item.Cost) then
					table.insert(sortedItems, {
						index = i,
						item = item,
						remaining = remaining,
						value = shop.GetItemValue(item),
					})
				end
			end
		end

		if #sortedItems == 0 then
			return false
		end

		table.sort(sortedItems, function(a, b)
			return a.value > b.value
		end)

		local bought = false
		for _, itemInfo in ipairs(sortedItems) do
			fireNetwork("BuyShopItem", shopId, itemInfo.index)
			bought = true
			local itemName = itemInfo.item.Product.Name or "Item"
			Library:Notify({
				Title = "Shop Purchase",
				Description = "Bought " .. itemName .. " from " .. shopId,
				Time = 5,
			})
			task.wait(0.5)
		end

		return bought
	end

	shop.GetItemValue = function(item)
		local product = item.Product
		local cost = item.Cost.Amount

		if cost <= 0 then
			return 1000000
		end

		if product.Type == "Pet" then
			local rarityValue = {
				Common = 1,
				Rare = 2,
				Epic = 3,
				Legendary = 4,
				Secret = 5,
				Unique = 6,
			}
			local rarity = product.Rarity or "Common"
			return (rarityValue[rarity] or 1) * 1000 / cost
		elseif product.Type == "Potion" then
			local level = product.Level or 1
			return level * 500 / cost
		elseif product.Type == "Powerup" then
			return 800 / cost
		elseif product.Type == "Currency" then
			local amount = product.Amount or 1
			return amount / cost
		end

		return 1 / cost
	end

	shop.CanReroll = function(shopId)
		local playerData = data.GetData()
		if not playerData then
			return false
		end

		local maxRerolls = shopUtil:GetMaxFreeRerolls(playerData)
		local usedRerolls = playerData.ShopFreeRerolls.Used

		return maxRerolls > usedRerolls
	end

	shop.RerollShop = function(shopId)
		if shop.CanReroll(shopId) then
			fireNetwork("ShopFreeReroll", shopId)
			Library:Notify({
				Title = "Shop Rerolled",
				Description = "Rerolled " .. shopId .. " for new items",
				Time = 5,
			})
			return true
		end
		return false
	end

	shop.AutoBuyShops = function()
		local bought = false

		if _G.Settings.ShopSettings.AlienShop and shop.CanBuyItems("alien-shop") then
			bought = shop.BuyBestItems("alien-shop") or bought
		end

		if _G.Settings.ShopSettings.ShardShop and shop.CanBuyItems("shard-shop") then
			bought = shop.BuyBestItems("shard-shop") or bought
		end

		if not bought and _G.Settings.ShopSettings.AutoReroll then
			if _G.Settings.ShopSettings.RerollShop == "alien-shop" and _G.Settings.ShopSettings.AlienShop then
				shop.RerollShop("alien-shop")
			elseif _G.Settings.ShopSettings.RerollShop == "shard-shop" and _G.Settings.ShopSettings.ShardShop then
				shop.RerollShop("shard-shop")
			end
		end

		return bought
	end
end

-- Added Auto Competition Module
local AutoCompetition = {}
do
	AutoCompetition.Settings = _G.Settings.AutoCompetitionSettings

	function AutoCompetition:GetCurrentQuests()
		local playerData = data.GetData()
		if not playerData then
			return {}
		end

		local competitive = playerData.Competitive
		if not competitive then
			return {}
		end

		local seasonId = CompetitiveShared:GetSeason()
		if not seasonId then
			return {}
		end

		local quests = {}
		for i = 1, 4 do
			local questId = CompetitiveShared.QuestKey .. "-" .. i
			local quest = QuestUtil:FindById(playerData, questId)

			if quest then
				local progress = quest.Progress[1]
				local requirement = QuestUtil:GetRequirement(quest.Tasks[1])
				local description = QuestUtil:FormatTask(quest.Tasks[1])

				table.insert(quests, {
					index = i,
					id = questId,
					description = description,
					progress = progress,
					requirement = requirement,
					permanent = i <= 2,
					reward = quest.Rewards[1],
					task = quest.Tasks[1],
				})
			end
		end

		return quests
	end

	function AutoCompetition:FindEggModel(eggName)
		local rendered = workspace.Rendered
		local worlds = workspace.Worlds

		-- Method 1: Check specific container in Rendered (heuristic from original script)
		if self.Settings.FindEggMethod == "Rendered" and #rendered:GetChildren() >= 12 then
			local eggContainer = rendered:GetChildren()[12]
			if eggContainer then
				if eggContainer.Name == eggName then return eggContainer end
				local found = eggContainer:FindFirstChild(eggName)
				if found then return found end
				-- Check children by name just in case
				for _, child in pairs(eggContainer:GetChildren()) do
					if child.Name == eggName then return child end
				end
			end
		end

		-- Method 2: Search through all children of Rendered
		if self.Settings.FindEggMethod == "Rendered" then
			for _, model in pairs(rendered:GetChildren()) do
				if model.Name == eggName then return model end
				local found = model:FindFirstChild(eggName, true) -- Recursive search
				if found then return found end
			end
		end

		-- Method 3: Search through Worlds (More standard location?)
		for _, world in pairs(worlds:GetChildren()) do
			if world:IsA("Folder") then
				local islands = world:FindFirstChild("Islands") or world
				for _, island in pairs(islands:GetChildren()) do
					local eggModel = island:FindFirstChild(eggName)
					if eggModel then return eggModel end
				end
			end
		end

		print("Could not find egg model for:", eggName)
		return nil
	end

	function AutoCompetition:HatchEggAtModel(eggName, eggModel)
		if not eggModel then
			self.Settings._Status = "Error: Could not find egg " .. eggName
			return false
		end

		local eggRoot = eggModel:FindFirstChild("Root") or eggModel:FindFirstChild("HumanoidRootPart") or eggModel:FindFirstChildOfClass("BasePart")
		if not eggRoot then
			self.Settings._Status = "Error: Could not find root part for egg " .. eggName
			return false
		end

		local playerRoot = lp.Character and lp.Character:FindFirstChild("HumanoidRootPart")
		if not playerRoot then
			self.Settings._Status = "Error: Player character not found."
			return false
		end

		-- Calculate target position near the egg
		local targetOffset = (playerRoot.Position - eggRoot.Position).Unit * 5
		local targetPosition = eggRoot.Position + targetOffset + Vector3.new(0, 3, 0) -- Adjusted Y offset

		if (playerRoot.Position - targetPosition).Magnitude > 5 then
			self.Settings._Status = "Moving to " .. eggName
			pcall(misc.tweenToPos, targetPosition)
			task.wait(0.5) -- Wait briefly after tweening
			-- Recheck distance after moving
			if (playerRoot.Position - targetPosition).Magnitude > 10 then
				self.Settings._Status = "Error: Failed to move close enough to " .. eggName
				return false
			end
		end

		self.Settings._Status = "Hatching " .. eggName
		local hatchAmount = misc.getHatchAmount()
		fireEvent("HatchEgg", eggName, hatchAmount)
		task.wait(0.2) -- Small delay after firing event
		return true
	end

	function AutoCompetition:HandleRarityHatchQuest(quest)
		local preferredEgg = self.Settings.EggPreferences.RarityHatch
		self.Settings._Status = "Handling Rarity Hatch: " .. quest.task.Rarity .. " (Using " .. preferredEgg .. ")"
		local eggModel = self:FindEggModel(preferredEgg)
		return self:HatchEggAtModel(preferredEgg, eggModel)
	end

	function AutoCompetition:HandleSpecificHatchQuest(quest)
		local eggName = quest.task.Egg
		self.Settings._Status = "Handling Specific Hatch: " .. eggName
		local eggModel = self:FindEggModel(eggName)
		return self:HatchEggAtModel(eggName, eggModel)
	end

	function AutoCompetition:HandleGenericHatchQuest(quest)
		local preferredEgg = self.Settings.EggPreferences.GenericHatch
		self.Settings._Status = "Handling Generic Hatch (Using " .. preferredEgg .. ")"
		local eggModel = self:FindEggModel(preferredEgg)
		return self:HatchEggAtModel(preferredEgg, eggModel)
	end

	function AutoCompetition:HandleShinyHatchQuest(quest)
		local preferredEgg = self.Settings.EggPreferences.ShinyHatch
		self.Settings._Status = "Handling Shiny Hatch (Using " .. preferredEgg .. ")"
		local eggModel = self:FindEggModel(preferredEgg)
		return self:HatchEggAtModel(preferredEgg, eggModel)
	end

	function AutoCompetition:ProcessQuests()
		local quests = self:GetCurrentQuests()
		if #quests == 0 then
			self.Settings._Status = "No active competitive quests found."
			return
		end

		-- Reroll Logic
		if self.Settings.AutoReroll then
			local rerollOccurred = false
			for _, quest in ipairs(quests) do
				-- Only reroll slots 3 and 4
				if quest.index == 3 or quest.index == 4 then
					local shouldReroll = false
					local reason = ""

					-- Reroll Mythic pet quest?
					if self.Settings.RerollMythic and quest.task.Type == "Hatch" and quest.task.Mythic then
						shouldReroll = true
						reason = "Mythic Pet Hatch"
					-- Reroll non-hatch quests?
					elseif self.Settings.RerollNonHatch and quest.task.Type ~= "Hatch" then
						shouldReroll = true
						reason = "Non-Hatch Quest (Type: " .. quest.task.Type .. ")"
					-- Reroll high requirement specific egg hatches?
					elseif quest.task.Type == "Hatch" and quest.task.Egg and quest.requirement > self.Settings.RerollThreshold then
						shouldReroll = true
						reason = "High Requirement (" .. quest.requirement .. ") for " .. quest.task.Egg
					end

					if shouldReroll then
						self.Settings._Status = "Rerolling quest " .. quest.index .. " (" .. reason .. ")"
						-- Assuming CompetitiveReroll uses fireNetwork, adjust if needed
						fireNetwork("CompetitiveReroll", quest.index)
						rerollOccurred = true
						task.wait(0.5) -- Delay after rerolling one
					end
				end
			end

			-- If a reroll happened, wait for next cycle to get updated quests
			if rerollOccurred then
				self.Settings._Status = "Waiting for quests to update after reroll..."
				return
			end
		end

		-- Quest Handling Logic based on Priority
		for _, priorityType in ipairs(self.Settings.PriorityOrder) do
			local handled = false
			if priorityType == "RarityHatch" then
				local rarityHatchQuests = {}
				for _, quest in ipairs(quests) do
					if quest.task.Type == "Hatch" and quest.task.Rarity and quest.progress < quest.requirement then
						table.insert(rarityHatchQuests, quest)
					end
				end
				if #rarityHatchQuests > 0 then
					-- Optional: Sort by rarity? For now, just take the first.
					handled = self:HandleRarityHatchQuest(rarityHatchQuests[1])
				end
			elseif priorityType == "SpecificHatch" then
				local specificHatchQuests = {}
				for _, quest in ipairs(quests) do
					if quest.task.Type == "Hatch" and quest.task.Egg and quest.progress < quest.requirement then
						table.insert(specificHatchQuests, quest)
					end
				end
				if #specificHatchQuests > 0 then
					-- Sort by lowest requirement first
					table.sort(specificHatchQuests, function(a, b)
						return a.requirement < b.requirement
					end)
					handled = self:HandleSpecificHatchQuest(specificHatchQuests[1])
				end
			elseif priorityType == "GenericHatch" then
				for _, quest in ipairs(quests) do
					if quest.task.Type == "Hatch" and not quest.task.Rarity and not quest.task.Egg and not quest.task.Shiny and not quest.task.Mythic and quest.progress < quest.requirement then
						handled = self:HandleGenericHatchQuest(quest)
						if handled then break end
					end
				end
			elseif priorityType == "ShinyHatch" then
				for _, quest in ipairs(quests) do
					if quest.task.Type == "Hatch" and quest.task.Shiny and quest.progress < quest.requirement then
						handled = self:HandleShinyHatchQuest(quest)
						if handled then break end
					end
				end
			end

			-- If a quest was handled in this priority level, move to the next cycle
			if handled then
				return
			end
		end

		-- If no quests were handled (e.g., all complete or non-hatch and reroll disabled)
		self.Settings._Status = "Idle - No actionable quests found in priority order."
	end

	function AutoCompetition:Start()
		if self.Settings._Running then
			return
		end
		self.Settings._Running = true
		self.Settings._Status = "Starting..."

		task.spawn(function()
			while self.Settings._Running do
				pcall(function()
					if not self.Settings.Enabled then
						self:Stop()
						return
					end
					self:ProcessQuests()
				end)
				task.wait(self.Settings.Delay)
			end
			self.Settings._Status = "Stopped"
		end)
	end

	function AutoCompetition:Stop()
		self.Settings._Running = false
		self.Settings._Status = "Stopping..."
	end
end

local repo = "https://raw.githubusercontent.com/deividcomsono/Obsidian/main/"
local Library = loadstring(game:HttpGet(repo .. "Library.lua"))()
local ThemeManager = loadstring(game:HttpGet(repo .. "addons/ThemeManager.lua"))()
local SaveManager = loadstring(game:HttpGet(repo .. "addons/SaveManager.lua"))()

local Options = Library.Options
local Toggles = Library.Toggles

Library.ForceCheckbox = true
Library.ShowToggleFrameInKeybinds = true

local Window = Library:CreateWindow({
	Title = "0ne Hub",
	Footer = "v1.0.3",
	ToggleKeybind = Enum.KeyCode.RightControl,
	Center = true,
	AutoShow = true,
	NotifySide = "Right",
	CornerRadius = 10,
	MobileButtonsSide = "Right",
})

local MainTab = Window:AddTab("Main", "house")
local AutomationTab = Window:AddTab("Automation", "brain-cog")
local UtilityTab = Window:AddTab("Utility", "anvil")
local SettingsTab = Window:AddTab("Settings", "cog")

local MenuSettingsBox = SettingsTab:AddLeftGroupbox("Menu Settings")
local AppearanceBox = SettingsTab:AddRightGroupbox("Appearance")

MenuSettingsBox:AddLabel("Menu Keybind"):AddKeyPicker("MenuKeybind", {
	Default = "RightControl",
	NoUI = true,
	Text = "Menu toggle key",
})

MenuSettingsBox:AddButton({
	Text = "Unload Script",
	Func = function()
		Library:Unload()
	end,
	DoubleClick = true,
	Tooltip = "Completely unload the script (requires double-click)",
})

AppearanceBox:AddToggle("ShowCustomCursor", {
	Text = "Custom Cursor",
	Default = true,
	Tooltip = "Enable or disable the custom cursor",
	Callback = function(Value)
		Library.ShowCustomCursor = Value
	end,
})

AppearanceBox:AddDropdown("NotificationSide", {
	Values = { "Left", "Right" },
	Default = "Right",
	Text = "Notification Side",
	Tooltip = "Choose which side notifications appear on",
	Callback = function(Value)
		Library:SetNotifySide(Value)
	end,
})

local FarmingBox = MainTab:AddLeftGroupbox("Farming")

--[[
	local BlowBubbleToggle = FarmingBox:AddToggle("AutoBlowBubble", {
		Text = "Auto Blow Bubble",
		Default = _G.Settings.AutoFarm,
		Tooltip = "Automatically blows bubbles",
		Callback = function(Value)
			_G.Settings.AutoFarm = Value
			while _G.Settings.AutoFarm do
				pcall(main.BlowAndSell)
				task.wait(0.5)
			end
		end,
	})
]]

local CollectToggle = FarmingBox:AddToggle("AutoCollect", {
	Text = "Auto Collect",
	Default = _G.Settings.AutoCollect,
	Tooltip = "Automatically collects pickups",
	Callback = function(Value)
		_G.Settings.AutoCollect = Value
		while _G.Settings.AutoCollect do
			pcall(main.CollectPickup)
			task.wait(0.2)
		end
	end,
})

local ChestToggle = FarmingBox:AddToggle("AutoClaimChests1", {
	Text = "Auto Claim Chests",
	Default = _G.Settings.AutoClaimChests,
	Tooltip = "Automatically claims available chests",
	Callback = function(Value)
		_G.Settings.AutoClaimChests = Value

		while _G.Settings.AutoClaimChests do
			pcall(main.ClaimChests)
			task.wait(0.5)
		end
	end,
})

local TeleportBox = MainTab:AddRightGroupbox("Teleport")

local TeleportToggle = TeleportBox:AddToggle("AutoTeleport", {
	Text = "Nonstop Island TP",
	Default = _G.Settings.AutoTeleport,
	Tooltip = "Automatically teleports to islands",
	Callback = function(Value)
		_G.Settings.AutoTeleport = Value
		while _G.Settings.AutoTeleport do
			pcall(main.TeleportToIslands, _G.Settings.IslandWait)
			task.wait(0.1)
		end
	end,
})

local IslandWaitSlider = TeleportBox:AddSlider("IslandWait", {
	Text = "Island Wait Time",
	Default = 5,
	Min = 1,
	Max = 20,
	Rounding = 1,
	Compact = false,
	Tooltip = "Time to wait between island teleports",
	Callback = function(Value)
		_G.Settings.IslandWait = Value
	end,
})

local UnlockIslands = TeleportBox:AddButton("Unlock Islands", {
	Text = "Unlock Islands",
	Tooltip = "Unlock all islands",
	Func = function()
		main.UnlockIslands()
	end,
})

local RewardsBox = MainTab:AddLeftGroupbox("Rewards")

local UseCodesButton = RewardsBox:AddButton("Use All Codes", {
	Text = "Redeem All Codes",
	Tooltip = "Redeem all available codes",
	Func = function()
		main.UseCodes()
	end,
})

local ClaimPlaytimeToggle = RewardsBox:AddToggle("Claim Playtime Rewards", {
	Text = "Claim Playtime Rewards",
	Default = _G.Settings.ClaimPlaytime,
	Tooltip = "Automatically claims playtime rewards",
	Callback = function(Value)
		_G.Settings.ClaimPlaytime = Value
		while _G.Settings.ClaimPlaytime do
			pcall(rewardClaim.PlaytimeRewards)
			task.wait(0.5)
		end
	end,
})

local MysteryBoxToggle = RewardsBox:AddToggle("AutoMysterybox", {
	Text = "Auto Open Mystery Box",
	Default = _G.Settings.AutoMysterybox,
	Tooltip = "Automatically Open mystery box",
	Callback = function(Value)
		_G.Settings.AutoMysterybox = Value
		while _G.Settings.AutoMysterybox do
			pcall(misc.AutoGift)
			task.wait(0.5)
		end
	end,
})

local SeasonRewardsToggle = RewardsBox:AddToggle("AutoClaimSeasonRewards", {
	Text = "Auto Claim Season Rewards",
	Default = _G.Settings.AutoClaimSeasonRewards,
	Tooltip = "Automatically claim season rewards when available",
	Callback = function(Value)
		_G.Settings.AutoClaimSeasonRewards = Value
		while _G.Settings.AutoClaimSeasonRewards do
			pcall(rewardClaim.SeasonRewards)
			task.wait(1)
		end
	end,
})

local MinigamesBox = MainTab:AddRightGroupbox("Minigames")

local DogMinigameToggle = MinigamesBox:AddToggle("AutoDogMinigame", {
	Text = "Auto Dog Minigame",
	Default = _G.Settings.AutoDogMinigame,
	Tooltip = "Automatically plays the dog minigame",
	Callback = function(Value)
		_G.Settings.AutoDogMinigame = Value
		while _G.Settings.AutoDogMinigame do
			pcall(misc.AutoDoggy)
			task.wait(1)
		end
	end,
})

local WheelSpinToggle = MinigamesBox:AddToggle("AutoWheelSpin", {
	Text = "Auto Wheel Spin",
	Default = _G.Settings.AutoWheelSpin,
	Tooltip = "Automatically claims and uses wheel spins",
	Callback = function(Value)
		_G.Settings.AutoWheelSpin = Value
		while _G.Settings.AutoWheelSpin do
			pcall(misc.AutoWheelSpin)
			task.wait(5)
		end
	end,
})

local GenieQuestToggle = MinigamesBox:AddToggle("AutoGenieQuest", {
	Text = "Auto Gem Genie Quest",
	Default = _G.Settings.AutoGenieQuest,
	Tooltip = "Automatically starts Gem Genie quests when available",
	Callback = function(Value)
		_G.Settings.AutoGenieQuest = Value
		while _G.Settings.AutoGenieQuest do
			pcall(misc.AutoGenieQuest)
			task.wait(2)
		end
	end,
})

local RiftBox = MainTab:AddLeftGroupbox("Rift")

local EnableToggle = RiftBox:AddToggle("EnableRift", {
	Text = "Enable Rift",
	Default = _G.Settings.RiftSettings.Enabled,
	Tooltip = "Enables Rift",
	Callback = function(Value)
		_G.Settings.RiftSettings.Enabled = Value
		while _G.Settings.RiftSettings.Enabled do
			task.wait(0.1)
			if
				_G.Settings.RiftSettings.AutoChest
				or _G.Settings.RiftSettings.AutoRoyal
				or _G.Settings.RiftSettings.AutoEgg
			then
				pcall(main.DoRifts)
			end
		end
	end,
})

local RiftChestToggle = RiftBox:AddToggle("AutoClaimChests", {
	Text = "Auto Claim Chests",
	Default = _G.Settings.RiftSettings.AutoChest,
	Tooltip = "Automatically claims available chests",
	Callback = function(Value)
		_G.Settings.RiftSettings.AutoChest = Value
	end,
})

local GoldenKeysSlider = RiftBox:AddSlider("MinGoldenKeys", {
	Text = "Min Golden Keys",
	Default = _G.Settings.RiftSettings.MinGoldenKeys,
	Min = 1,
	Max = 100,
	Rounding = 0,
	Compact = false,
	Tooltip = "Minimum number of Golden Keys required before claiming a Golden Chest",
	Callback = function(Value)
		_G.Settings.RiftSettings.MinGoldenKeys = Value
	end,
})

local RoyalChestToggle = RiftBox:AddToggle("AutoClaimRoyalChests", {
	Text = "Auto Claim Royal Chests",
	Default = _G.Settings.RiftSettings.AutoRoyal,
	Tooltip = "Automatically claims available royal chests",
	Callback = function(Value)
		_G.Settings.RiftSettings.AutoRoyal = Value
	end,
})

local RoyalKeysSlider = RiftBox:AddSlider("MinRoyalKeys", {
	Text = "Min Royal Keys",
	Default = _G.Settings.RiftSettings.MinRoyalKeys,
	Min = 1,
	Max = 100,
	Rounding = 0,
	Compact = false,
	Tooltip = "Minimum number of Royal Keys required before claiming a Royal Chest",
	Callback = function(Value)
		_G.Settings.RiftSettings.MinRoyalKeys = Value
	end,
})

local EggToggle = RiftBox:AddToggle("AutoClaimEggs", {
	Text = "Auto Island Eggs",
	Default = _G.Settings.RiftSettings.AutoEgg,
	Tooltip = "Automatically goes to available eggs",
	Callback = function(Value)
		_G.Settings.RiftSettings.AutoEgg = Value
	end,
})

local EggMultiDropdown = RiftBox:AddDropdown("EggMulti", {
	Text = "Egg Multiplier",
	Default = _G.Settings.RiftSettings.EggOptions,
	Multi = true,
	Values = _G.Settings.RiftSettings.EggOptions,
	Callback = function(Value)
		for i, v in next, Value do
			if v then
				table.insert(_G.Settings.RiftSettings.EggSelected, i)
			else
				pcall(function()
					table.remove(
						_G.Settings.RiftSettings.EggSelected,
						table.find(_G.Settings.RiftSettings.EggSelected, i)
					)
				end)
			end
		end
	end,
})

local RiftEggsBox = MainTab:AddRightGroupbox("Rift Eggs")

local EggLabel = RiftEggsBox:AddLabel("Select Eggs to Hatch")
local eggs = misc.getOrderedEggs()
local eggToggles = {}
for _, v in next, eggs do
	eggToggles[v] = RiftEggsBox:AddToggle(v, {
		Text = v,
		Default = false,
		Tooltip = "Automatically goes to available eggs",
		Callback = function(Value)
			if Value then
				table.insert(_G.Settings.RiftSettings.Eggs, misc.findInRift(v))
			else
				pcall(function()
					table.remove(
						_G.Settings.RiftSettings.Eggs,
						table.find(_G.Settings.RiftSettings.Eggs, misc.findInRift(v))
					)
				end)
			end

			table.sort(_G.Settings.RiftSettings.Eggs, function(a, b)
				return egger[misc.findOutRift(a)].LayoutOrder > egger[misc.findOutRift(b)].LayoutOrder
			end)
		end,
	})
end

local hatchEgg = require(game:GetService("ReplicatedStorage").Client.Effects.HatchEgg)

local function hookHatchEgg()
	local success, result = pcall(function()
		local originalPlay = hatchEgg.Play
		local originalDisplayPetOnce = hatchEgg.DisplayPetOnce

		hatchEgg.Play = function(eggName, petData, speed, callback, skipZoom)
			pcall(function()
				local properEggName = eggName
				pcall(function()
					if type(eggName) == "table" then
						if eggName.Name then
							properEggName = eggName.Name
						elseif eggName.Egg then
							properEggName = eggName.Egg
						end
					elseif type(eggName) ~= "string" then
						for eggId, eggData in pairs(egger) do
							if eggData.Id == eggName then
								properEggName = eggId
								break
							end
						end
					end

					if (properEggName == eggName or properEggName == "Unknown Egg") and petData then
						if type(petData) == "table" and petData.Name then
							local gameModules = game:GetService("ReplicatedStorage").Shared.Data
							local petsModule = require(gameModules.Pets)

							if petsModule[petData.Name] and petsModule[petData.Name].Egg then
								properEggName = petsModule[petData.Name].Egg
							end
						elseif type(petData) == "table" and petData.Pets then
							for _, petInfo in pairs(petData.Pets) do
								if type(petInfo) == "table" and petInfo.Pet and petInfo.Pet.Name then
									local gameModules = game:GetService("ReplicatedStorage").Shared.Data
									local petsModule = require(gameModules.Pets)

									if petsModule[petInfo.Pet.Name] and petsModule[petInfo.Pet.Name].Egg then
										properEggName = petsModule[petInfo.Pet.Name].Egg
										break
									end
								end
							end
						end
					end
				end)

				if not _G.Settings.RemoveHatchAnim then
					originalPlay(eggName, petData, speed, callback, skipZoom)
				else
					if callback then
						callback()
					end
				end

				if _G.Settings.WebhookSettings.Enabled and petData then
					if type(petData) == "table" and petData.Pets then
						for _, petInfo in pairs(petData.Pets) do
							if type(petInfo) == "table" and petInfo.Pet then
								sendPetWebhook(petInfo.Pet, properEggName)
							end
						end
					elseif type(petData) == "table" and petData.Name then
						sendPetWebhook(petData, properEggName)
					end
				end
			end)
		end

		hatchEgg.DisplayPetOnce = function(self, pet, egg)
			pcall(function()
				local properEggName = egg
				pcall(function()
					if type(egg) == "table" then
						if egg.Name then
							properEggName = egg.Name
						elseif egg.Egg then
							properEggName = egg.Egg
						end
					elseif type(egg) ~= "string" then
						for eggId, eggData in pairs(egger) do
							if eggData.Id == egg then
								properEggName = eggId
								break
							end
						end
					end

					if (properEggName == egg or properEggName == "Unknown Egg") and pet and pet.Name then
						local gameModules = game:GetService("ReplicatedStorage").Shared.Data
						local petsModule = require(gameModules.Pets)

						if petsModule[pet.Name] and petsModule[pet.Name].Egg then
							properEggName = petsModule[pet.Name].Egg
						end
					end
				end)

				originalDisplayPetOnce(self, pet, egg)

				if _G.Settings.WebhookSettings.Enabled and pet then
					if type(pet) == "table" then
						if pet.Name then
							sendPetWebhook(pet, properEggName)
						elseif pet.Pet and type(pet.Pet) == "table" and pet.Pet.Name then
							sendPetWebhook(pet.Pet, properEggName)
						end
					end
				end
			end)
		end

		return hatchEgg.Play
	end)

	return success and result or hatchEgg.Play
end

function sendPetWebhook(pet, eggName)
	local success, result = pcall(function()
		if not pet then
			return false
		end
		if type(pet) ~= "table" then
			return false
		end
		if not pet.Name then
			return false
		end

		if type(pet.Name) ~= "string" then
			return false
		end

		local eggNameStr = "Unknown Egg"
		pcall(function()
			if eggName then
				if type(eggName) == "string" then
					eggNameStr = eggName
				elseif type(eggName) == "table" then
					if eggName.Name then
						eggNameStr = tostring(eggName.Name)
					elseif eggName.Egg then
						eggNameStr = tostring(eggName.Egg)
					else
						for eggId, eggData in pairs(egger) do
							if eggData.Id == eggName then
								eggNameStr = eggId
								break
							end
						end

						if eggNameStr == "Unknown Egg" then
							for riftId, riftData in pairs(rift) do
								if riftId == eggName or riftData.Egg == eggName then
									eggNameStr = riftData.Egg or riftId
									break
								end
							end
						end
					end
				else
					for eggId, eggData in pairs(egger) do
						if eggData.Id == eggName then
							eggNameStr = eggId
							break
						end
					end
				end
			end

			if eggNameStr == "Unknown Egg" and pet and pet.Name then
				local gameModules = game:GetService("ReplicatedStorage").Shared.Data
				local petsModule = require(gameModules.Pets)

				if petsModule[pet.Name] and petsModule[pet.Name].Egg then
					eggNameStr = petsModule[pet.Name].Egg
				end
			end
		end)

		local petData = petsData[pet.Name]
		if not petData then
			return false
		end

		local rarity = petData.Rarity
		if not rarity or type(rarity) ~= "string" then
			rarity = "Unknown"
		end

		local shouldNotify = false

		if _G.Settings.WebhookSettings.Enabled then
			local rarityValues = {
				Common = 1,
				Rare = 2,
				Epic = 3,
				Legendary = 4,
				Secret = 5,
				Mythic = 6,
			}

			local minRarityValue = rarityValues[_G.Settings.WebhookSettings.MinimumRarity] or 4
			local petRarityValue = rarityValues[rarity] or 1

			if petRarityValue >= minRarityValue then
				shouldNotify = true
			end

			if shouldNotify then
				local raritySettings = {
					Legendary = "NotifyLegendary",
					Secret = "NotifySecret",
					Mythic = "NotifyMythic",
				}

				if raritySettings[rarity] and not _G.Settings.WebhookSettings[raritySettings[rarity]] then
					shouldNotify = false
				end
			end

			if not shouldNotify and pet.Shiny and _G.Settings.WebhookSettings.NotifyShiny then
				shouldNotify = petRarityValue >= minRarityValue
			end
		end

		if not shouldNotify then
			return false
		end

		local imageUrl = ""
		pcall(function()
			local gameModules = game:GetService("ReplicatedStorage").Shared.Data
			local petsModule = require(gameModules.Pets)

			if petsModule[pet.Name] and petsModule[pet.Name].Images then
				local imagePath
				if pet.Shiny and petsModule[pet.Name].Images.Shiny then
					imagePath = petsModule[pet.Name].Images.Shiny
				elseif petsModule[pet.Name].Images.Normal then
					imagePath = petsModule[pet.Name].Images.Normal
				end

				if imagePath then
					local assetId
					if type(imagePath) == "string" then
						if imagePath:match("^rbxassetid://(%d+)$") then
							assetId = imagePath:match("^rbxassetid://(%d+)$")
						-- Or just a plain ID
						elseif imagePath:match("^(%d+)$") then
							assetId = imagePath
						end
					elseif type(imagePath) == "number" then
						assetId = tostring(imagePath)
					end

					if assetId then
						-- Use the Roblox thumbnail API method (2025 approach)
						local requestFunc
						if syn and syn.request then
							requestFunc = syn.request
						elseif http and http.request then
							requestFunc = http.request
						elseif request then
							requestFunc = request
						elseif http_request then
							requestFunc = http_request
						end

						if requestFunc then
							local thumbnailApiUrl = "https://thumbnails.roblox.com/v1/assets?assetIds="
								.. assetId
								.. "&size=512x512&format=Png&isCircular=false"
							print("Getting thumbnail data from:", thumbnailApiUrl)

							local response = requestFunc({
								Url = thumbnailApiUrl,
								Method = "GET",
							})

							if response and response.StatusCode == 200 then
								local success, thumbnailData = pcall(function()
									return HttpService:JSONDecode(response.Body)
								end)

								if
									success
									and thumbnailData
									and thumbnailData.data
									and thumbnailData.data[1]
									and thumbnailData.data[1].imageUrl
								then
									imageUrl = thumbnailData.data[1].imageUrl
									print("Successfully retrieved direct CDN URL:", imageUrl)
								else
									print("Failed to parse thumbnail JSON:", response.Body)
								end
							else
								print("Thumbnail API request failed:", response and response.StatusCode)
							end
						end

						if imageUrl == "" then
							print("Falling back to old URL methods")
							imageUrl = "https://www.roblox.com/asset-thumbnail/image?assetId="
								.. assetId
								.. "&width=420&height=420&format=png"
						end
					end
				end
			end

			-- If no image was found using normal methods, try a different approach
			if imageUrl == "" then
				-- Try to get the pet's icon ID from another source
				if petsData[pet.Name] and petsData[pet.Name].Icon then
					local iconId = petsData[pet.Name].Icon
					if type(iconId) == "string" then
						iconId = iconId:match("^rbxassetid://(%d+)$") or iconId
					end

					if iconId then
						-- Try the thumbnail API for this icon ID too
						local requestFunc
						if syn and syn.request then
							requestFunc = syn.request
						elseif http and http.request then
							requestFunc = http.request
						elseif request then
							requestFunc = request
						elseif http_request then
							requestFunc = http_request
						end

						if requestFunc then
							local thumbnailApiUrl = "https://thumbnails.roblox.com/v1/assets?assetIds="
								.. iconId
								.. "&size=512x512&format=Png&isCircular=false"
							local response = requestFunc({
								Url = thumbnailApiUrl,
								Method = "GET",
							})

							if response and response.StatusCode == 200 then
								local success, thumbnailData = pcall(function()
									return HttpService:JSONDecode(response.Body)
								end)

								if
									success
									and thumbnailData
									and thumbnailData.data
									and thumbnailData.data[1]
									and thumbnailData.data[1].imageUrl
								then
									imageUrl = thumbnailData.data[1].imageUrl
									print("Successfully retrieved direct CDN URL from icon:", imageUrl)
								end
							end
						end

						-- If the thumbnail API failed, try the fallback
						if imageUrl == "" then
							imageUrl = "https://tr.rbxcdn.com/" .. iconId .. "/512/512/Image/Png"
							print("Using fallback CDN URL:", imageUrl)
						end
					end
				end
			end
		end)

		local stats = {}
		if petData.Stats then
			for statName, statValue in pairs(petData.Stats) do
				table.insert(stats, statName .. ": " .. tostring(statValue))
			end
		end

		local rarityColors = {
			Common = 11184810,
			Rare = 5592575,
			Epic = 11141290,
			Legendary = 16750080,
			Secret = 16724341,
			Mythic = 5636095,
		}

		local chance = ""
		pcall(function()
			local statsUtil = require(game:GetService("ReplicatedStorage").Shared.Utils.Stats.PetUtil)
			local petChance = statsUtil:GetChance(pet)
			if petChance and type(petChance) == "number" then
				local chanceValue = 100 / petChance
				chance = string.format("1 in %s", math.ceil(chanceValue))
			end
		end)

		local statEmojis = {
			Bubbles = "🫧",
			Coins = "💰",
			Gems = "💎",
			XP = "✨",
		}

		local rarityEmojis = {
			Common = "⚪",
			Rare = "🔵",
			Epic = "🟣",
			Legendary = "🟠",
			Secret = "🔴",
			Mythic = "🔷",
		}

		local formattedStats = {}
		if petData.Stats then
			for statName, statValue in pairs(petData.Stats) do
				local emoji = statEmojis[statName] or "📊"
				table.insert(formattedStats, emoji .. " **" .. statName .. ":** " .. tostring(statValue))
			end
		end

		local color = rarityColors[rarity] or 16777215
		local rarityEmoji = rarityEmojis[rarity] or "⚪"

		local title = ""
		if pet.Mythic then
			title = "🔮 Mythic " .. pet.Name
		elseif pet.Shiny then
			title = "✨ Shiny " .. pet.Name
		else
			title = rarityEmoji .. " " .. pet.Name
		end

		local description = ""
		description = description .. "**Rarity:** " .. rarityEmoji .. " " .. rarity

		if chance ~= "" then
			description = description .. "\n**Chance:** 🎲 " .. chance
		end

		if #formattedStats > 0 then
			description = description .. "\n\n**Stats:**\n" .. table.concat(formattedStats, "\n")
		end

		if pet.Enchants and type(pet.Enchants) == "table" then
			description = description .. "\n\n**Enchants:**"
			for slotIdx, enchant in pairs(pet.Enchants) do
				if type(enchant) == "table" and enchant.Id and enchant.Level then
					local enchantName = tostring(enchant.Id):gsub("-", " ")
					enchantName = enchantName:gsub("^%l", string.upper)
					description = description
						.. "\n🔮 Slot "
						.. tostring(slotIdx)
						.. ": **"
						.. enchantName
						.. "** - Level "
						.. tostring(enchant.Level)
				end
			end
		end

		if pet.XP then
			description = description .. "\n\n**XP:** ✨ " .. tostring(pet.XP)
		end

		local playerData = {}
		pcall(function()
			playerData = data.GetData() or {}
		end)

		local eggStats = {}
		pcall(function()
			if playerData.EggsOpened and playerData.EggsOpened[eggNameStr] then
				eggStats.opened = playerData.EggsOpened[eggNameStr]
			else
				eggStats.opened = 0
			end

			if playerData.Stats and playerData.Stats.Hatches then
				eggStats.totalHatches = playerData.Stats.Hatches
			else
				eggStats.totalHatches = 0
			end
		end)

		local currencyInfo = {}
		pcall(function()
			currencyInfo.coins = playerData.Coins or 0
			currencyInfo.gems = playerData.Gems or 0

			if playerData.Bubble then
				currencyInfo.bubbleType = playerData.Bubble.Flavor or "Unknown"
				currencyInfo.bubbleAmount = playerData.Bubble.Amount or 0
				currencyInfo.bubbleGum = playerData.Bubble.Gum or "Unknown"
			end
		end)

		local fields = {}

		table.insert(fields, {
			name = "💰 Currency Info",
			value = string.format(
				"💰 **Coins:** %s\n💎 **Gems:** %s\n🫧 **Bubbles:** %s",
				misc.formatNumber(currencyInfo.coins or 0),
				misc.formatNumber(currencyInfo.gems or 0),
				misc.formatNumber(currencyInfo.bubbleAmount or 0)
			),
			inline = true,
		})

		table.insert(fields, {
			name = "📊 Egg Statistics",
			value = string.format(
				"🥚 **%s Opened:** %s\n📈 **Total Hatches:** %s",
				eggNameStr,
				misc.formatNumber(eggStats.opened or 0),
				misc.formatNumber(eggStats.totalHatches or 0)
			),
			inline = true,
		})

		local petInfo = {}
		pcall(function()
			local petCount = 0
			if playerData.Pets then
				petCount = #playerData.Pets
			end

			local maxStorage = 0
			pcall(function()
				maxStorage = statsutil:GetMaxPetStorage(playerData)
			end)

			table.insert(fields, {
				name = "🐶 Pet Information",
				value = string.format(
					"🐱 **Pets Owned:** %s\n💼 **Storage:** %s/%s",
					misc.formatNumber(petCount),
					misc.formatNumber(petCount),
					misc.formatNumber(maxStorage)
				),
				inline = true,
			})
		end)

		local webhookEmbed = nil
		local webhookContent = ""

		if _G.Settings.WebhookSettings.ModernStyle then
			-- Modern style with cleaner formatting
			local petEmoji = ""
			local petColor = rarityColors[rarity] or 16777215

			-- Set emoji based on rarity
			if pet.Mythic then
				petEmoji = "🔮"
			elseif pet.Shiny then
				petEmoji = "✨"
			else
				petEmoji = rarityEmojis[rarity] or "⚪"
			end

			-- Create a single, clean embed
			webhookEmbed = {
				{
					author = {
						name = "New Pet Hatched",
						icon_url = "https://i.imgur.com/AaFDYPO.png", -- Egg icon
					},
					title = petEmoji .. " " .. pet.Name,
					description = "**Rarity:** " .. rarity,
					color = petColor,
					thumbnail = imageUrl ~= "" and {
						url = imageUrl,
					} or nil,
					fields = {
						{
							name = "Pet Details",
							value = (chance ~= "" and "Chance: " .. chance .. "\n" or "")
								.. (pet.Shiny and "✨ Shiny" or "")
								.. (pet.Mythic and (pet.Shiny and " • " or "") .. "🔮 Mythic" or ""),
							inline = true,
						},
						{
							name = "Egg Source",
							value = "🥚 " .. eggNameStr,
							inline = true,
						},
					},
					footer = {
						text = "0ne Hub • " .. os.date("%Y-%m-%d %H:%M:%S"),
					},
				},
			}

			-- Add stats only if they exist
			if #formattedStats > 0 then
				table.insert(webhookEmbed[1].fields, {
					name = "Pet Stats",
					value = table.concat(formattedStats, "\n"),
					inline = false,
				})
			end

			-- Add enchants only if they exist
			if pet.Enchants and type(pet.Enchants) == "table" and next(pet.Enchants) then
				local enchantsList = {}
				for slotIdx, enchant in pairs(pet.Enchants) do
					if type(enchant) == "table" and enchant.Id and enchant.Level then
						local enchantName = tostring(enchant.Id):gsub("-", " ")
						enchantName = enchantName:gsub("^%l", string.upper)
						table.insert(enchantsList, "🔮 **" .. enchantName .. "** - Level " .. tostring(enchant.Level))
					end
				end

				if #enchantsList > 0 then
					table.insert(webhookEmbed[1].fields, {
						name = "Enchants",
						value = table.concat(enchantsList, "\n"),
						inline = false,
					})
				end
			end

			-- Create a clean, simple ping message if enabled
			if _G.Settings.WebhookSettings.PingEveryone then
				webhookContent = "@everyone"
			end

			if rarity == "Legendary" then
				webhookContent = webhookContent .. (webhookContent ~= "" and " " or "") .. "🌟 **LEGENDARY PET!**"
			elseif rarity == "Secret" then
				webhookContent = webhookContent .. (webhookContent ~= "" and " " or "") .. "🔥 **SECRET PET!**"
			elseif rarity == "Mythic" or pet.Mythic then
				webhookContent = webhookContent .. (webhookContent ~= "" and " " or "") .. "🔮 **MYTHIC PET!**"
			elseif pet.Shiny then
				webhookContent = webhookContent .. (webhookContent ~= "" and " " or "") .. "✨ **SHINY PET!**"
			end
		else
			-- Original style
			webhookEmbed = {
				{
					title = title,
					description = description,
					color = color,
					thumbnail = imageUrl ~= "" and {
						url = imageUrl,
					} or nil,
					fields = fields,
					footer = {
						text = "🥚 Hatched from " .. eggNameStr .. " • " .. os.date("%Y-%m-%d %H:%M:%S"),
					},
					timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ"),
				},
			}

			if _G.Settings.WebhookSettings.PingEveryone then
				webhookContent = "@everyone "
			end

			if rarity == "Legendary" then
				webhookContent = webhookContent
					.. "🌟🌟🌟 **LEGENDARY PET HATCHED!** 🌟🌟🌟\n> 👑 **"
					.. pet.Name
					.. "** has been hatched from **"
					.. eggNameStr
					.. "**!"
			elseif rarity == "Secret" then
				webhookContent = webhookContent
					.. "🔥🔥🔥 **SECRET PET HATCHED!** 🔥🔥🔥\n> 💥 **"
					.. pet.Name
					.. "** has been hatched from **"
					.. eggNameStr
					.. "**!"
			elseif rarity == "Mythic" or pet.Mythic then
				webhookContent = webhookContent
					.. "🔮🔮🔮 **MYTHIC PET HATCHED!** 🔮🔮🔮\n> 🌌 **"
					.. pet.Name
					.. "** has been hatched from **"
					.. eggNameStr
					.. "**!"
			elseif pet.Shiny then
				webhookContent = webhookContent
					.. "✨✨✨ **SHINY PET HATCHED!** ✨✨✨\n> 💫 **"
					.. pet.Name
					.. "** has been hatched from **"
					.. eggNameStr
					.. "**!"
			else
				webhookContent = webhookContent
					.. "🎉 **"
					.. rarity
					.. " PET HATCHED!** 🎉\n> 🥚 **"
					.. pet.Name
					.. "** has been hatched from **"
					.. eggNameStr
					.. "**!"
			end
		end

		local success = webhook.send(webhookContent, webhookEmbed)

		pcall(function()
			Library:Notify({
				Title = "Webhook Notification",
				Description = "Sent webhook for " .. pet.Name,
				Time = 3,
			})
		end)

		return true
	end)

	return success and result
end

local AutoShopBox = UtilityTab:AddLeftGroupbox("Auto Shop")

local ShopToggle = AutoShopBox:AddToggle("EnableShop", {
	Text = "Auto Buy from Shops",
	Default = _G.Settings.ShopSettings.Enabled,
	Tooltip = "Automatically buys the best items from shops",
	Callback = function(Value)
		_G.Settings.ShopSettings.Enabled = Value

		while _G.Settings.ShopSettings.Enabled do
			pcall(shop.AutoBuyShops)
			task.wait(0.2)
		end
	end,
})

local AlienShopToggle = AutoShopBox:AddToggle("AlienShop", {
	Text = "Buy from Alien Shop",
	Default = _G.Settings.ShopSettings.AlienShop,
	Tooltip = "Enable buying from the Alien Shop",
	Callback = function(Value)
		_G.Settings.ShopSettings.AlienShop = Value
	end,
})

local ShardShopToggle = AutoShopBox:AddToggle("ShardShop", {
	Text = "Buy from Shard Shop",
	Default = _G.Settings.ShopSettings.ShardShop,
	Tooltip = "Enable buying from the Shard Shop",
	Callback = function(Value)
		_G.Settings.ShopSettings.ShardShop = Value
	end,
})

local AutoRerollToggle = AutoShopBox:AddToggle("AutoReroll", {
	Text = "Auto Reroll Shop",
	Default = _G.Settings.ShopSettings.AutoReroll,
	Tooltip = "Automatically reroll shop when no items are available to buy",
	Callback = function(Value)
		_G.Settings.ShopSettings.AutoReroll = Value
	end,
})

local RerollShopDropdown = AutoShopBox:AddDropdown("RerollShop", {
	Text = "Shop to Reroll",
	Values = { "alien-shop", "shard-shop" },
	Default = _G.Settings.ShopSettings.RerollShop,
	Tooltip = "Select which shop to auto reroll",
	Callback = function(Value)
		_G.Settings.ShopSettings.RerollShop = Value
	end,
})

local ManualShopBox = UtilityTab:AddRightGroupbox("Manual Shop")

local AlienShopButton = ManualShopBox:AddButton("Buy from Alien Shop", {
	Text = "Buy Best Items from Alien Shop",
	Tooltip = "Manually buy the best items from the Alien Shop",
	Func = function()
		pcall(shop.BuyBestItems, "alien-shop")
	end,
})

local ShardShopButton = ManualShopBox:AddButton("Buy from Shard Shop", {
	Text = "Buy Best Items from Shard Shop",
	Tooltip = "Manually buy the best items from the Shard Shop",
	Func = function()
		pcall(shop.BuyBestItems, "shard-shop")
	end,
})

local RerollAlienShopButton = ManualShopBox:AddButton("Reroll Alien Shop", {
	Text = "Reroll Alien Shop",
	Tooltip = "Manually reroll the Alien Shop",
	Func = function()
		pcall(shop.RerollShop, "alien-shop")
	end,
})

local RerollShardShopButton = ManualShopBox:AddButton("Reroll Shard Shop", {
	Text = "Reroll Shard Shop",
	Tooltip = "Manually reroll the Shard Shop",
	Func = function()
		pcall(shop.RerollShop, "shard-shop")
	end,
})

-- Apply the hook
local newHatchF = hookHatchEgg()

local HatchingBox = AutomationTab:AddLeftGroupbox("Hatching")

local RemoveHatchAnimToggle = HatchingBox:AddToggle("Remove Hatch Animation", {
	Text = "Remove Hatch Animation",
	Default = _G.Settings.RemoveHatchAnim,
	Tooltip = "Removes the hatch animation",
	Callback = function(Value)
		_G.Settings.RemoveHatchAnim = Value
		if _G.Settings.RemoveHatchAnim then
			hatchEgg.Play = function(eggName, petData, speed, callback, skipZoom)
				if callback then
					callback()
				end

				-- Still process for webhook
				newHatchF(eggName, petData, speed, nil, true)
			end
		else
			hatchEgg.Play = newHatchF
		end
	end,
})

local EnchantBox = AutomationTab:AddLeftGroupbox("Auto Enchant")

local equippedPetsInfo, equippedPetsDropdown = enchant.GetEquippedPets()

local PetDropdown = EnchantBox:AddDropdown("PetSelection", {
	Text = "Select Pet",
	Values = equippedPetsDropdown,
	Default = 1,
	Searchable = true,
	Tooltip = "Select a pet to enchant",
	Callback = function(Value)
		_G.Settings.EnchantSettings.SelectedPet = Value
		_G.Settings.EnchantSettings.SelectedPetId = equippedPetsInfo[Value].Id
	end,
})

local RefreshPetsButton = EnchantBox:AddButton("RefreshPets", {
	Text = "Refresh Pets List",
	Tooltip = "Refresh the list of equipped pets",
	Func = function()
		equippedPetsInfo, equippedPetsDropdown = enchant.GetEquippedPets()
		PetDropdown:SetValues(equippedPetsDropdown)
		Library:Notify({
			Title = "Pets Refreshed",
			Description = "Pet list has been updated",
			Time = 3,
		})
	end,
})

local availableEnchants = enchant.GetAvailableEnchants()

local EnchantDropdown = EnchantBox:AddDropdown("EnchantSelection", {
	Text = "Target Enchant",
	Values = availableEnchants,
	Default = "team-up",
	Tooltip = "Select the enchant you want to get",
	Callback = function(Value)
		_G.Settings.EnchantSettings.TargetEnchant = Value
	end,
})

local SlotDropdown = EnchantBox:AddDropdown("SlotSelection", {
	Text = "Enchant Slot",
	Values = { 1, 2 },
	Default = 1,
	Tooltip = "Select which enchant slot to reroll (slot 2 requires shiny pet)",
	Callback = function(Value)
		_G.Settings.EnchantSettings.SelectedSlot = Value
	end,
})

local UseRerollOrbsToggle = EnchantBox:AddToggle("UseRerollOrbs", {
	Text = "Use Reroll Orbs",
	Default = false,
	Tooltip = "Use Reroll Orbs for single slot rerolls (costs 1 orb per attempt). Otherwise uses gems to reroll all slots.",
	Callback = function(Value)
		_G.Settings.EnchantSettings.UseRerollOrbs = Value
	end,
})

local MinLevelSlider = EnchantBox:AddSlider("MinLevel", {
	Text = "Minimum Level",
	Default = 3,
	Min = 1,
	Max = 5,
	Rounding = 0,
	Compact = false,
	Tooltip = "Minimum enchant level to accept",
	Callback = function(Value)
		_G.Settings.EnchantSettings.MinLevel = Value
	end,
})

local MaxAttemptsSlider = EnchantBox:AddSlider("MaxAttempts", {
	Text = "Max Attempts",
	Default = 100,
	Min = 10,
	Max = 500,
	Rounding = 0,
	Compact = false,
	Tooltip = "Maximum number of reroll attempts",
	Callback = function(Value)
		_G.Settings.EnchantSettings.MaxAttempts = Value
	end,
})

local StatusLabel = EnchantBox:AddLabel({ Text = "Status: Idle", DoesWrap = true })

task.spawn(function()
	while true do
		StatusLabel:SetText("Status: " .. _G.Settings.EnchantSettings.Status)
		task.wait(0.1)
	end
end)

local StartStopToggle = EnchantBox:AddToggle("StartStop", {
	Text = "Start Auto Enchant",
	Default = false,
	Tooltip = "Start or stop the auto enchant process",
	Callback = function(Value)
		_G.Settings.EnchantSettings.Enabled = Value

		if Value then
			task.spawn(enchant.AutoEnchant)
		else
			_G.Settings.EnchantSettings.Status = "Stopped by user"
		end
	end,
})

local PotionsBox = AutomationTab:AddRightGroupbox("Auto Potions")

local allPotions = potion.GetAllPotions()
local potionDropdownValues = {}

for _, potionData in pairs(allPotions) do
	table.insert(potionDropdownValues, potionData.DisplayName)
end

local PotionsDropdown = PotionsBox:AddDropdown("SelectedPotions", {
	Text = "Select Potions",
	Tooltip = "Select potions to automatically use",
	Values = potionDropdownValues,
	Multi = true,
	Default = _G.Settings.PotionSettings.SelectedPotions,
	Callback = function(Value)
		_G.Settings.PotionSettings.SelectedPotions = {}
		for potionName, selected in pairs(Value) do
			if selected then
				table.insert(_G.Settings.PotionSettings.SelectedPotions, potionName)
			end
		end
	end,
})

local SmartInfinityToggle = PotionsBox:AddToggle("SmartInfinity", {
	Text = "Smart Infinity Elixir",
	Tooltip = "Only use Infinity Elixir when Lucky Evolved and Speed Evolved are active",
	Default = _G.Settings.PotionSettings.SmartInfinity,
	Callback = function(Value)
		_G.Settings.PotionSettings.SmartInfinity = Value
	end,
})

local AutoPotionToggle = PotionsBox:AddToggle("AutoPotion", {
	Text = "Auto Use Potions",
	Tooltip = "Automatically use selected potions when they're not active",
	Default = _G.Settings.PotionSettings.Enabled,
	Callback = function(Value)
		_G.Settings.PotionSettings.Enabled = Value
		while _G.Settings.PotionSettings.Enabled do
			pcall(potion.AutoUsePotion)
			task.wait(0.2)
		end
	end,
})

misc.setupAntiAFK()

local InfoBox = SettingsTab:AddRightGroupbox("Info")
local DiscordButton = InfoBox:AddButton("Discord", {
	Text = "Join our Discord",
	Func = function()
		setclipboard("https://discord.gg/fm57R8mG")
		Library:Notify({
			Title = "Copied to clipboard",
			Description = "Discord link copied to clipboard!",
			Time = 5,
		})
	end,
})

local WebhookBox = SettingsTab:AddLeftGroupbox("Webhook Settings")

WebhookBox:AddLabel("Discord Webhook URL")
WebhookBox:AddInput("WebhookURL", {
	Default = webhook.url,
	Numeric = false,
	Finished = true,
	Text = "Webhook URL",
	Tooltip = "Enter your Discord webhook URL",
	Placeholder = "https://discord.com/api/webhooks/...",
	Callback = function(Value)
		webhook.url = Value
		Library:Notify({
			Title = "Webhook Updated",
			Description = "Your webhook URL has been updated",
			Time = 3,
		})
	end,
})

WebhookBox:AddToggle("EnableWebhook", {
	Text = "Enable Webhook Notifications",
	Default = _G.Settings.WebhookSettings.Enabled,
	Tooltip = "Enable webhook notifications for egg hatches",
	Callback = function(Value)
		_G.Settings.WebhookSettings.Enabled = Value
	end,
})

WebhookBox:AddDropdown("MinimumRarity", {
	Text = "Minimum Rarity",
	Values = { "Common", "Rare", "Epic", "Legendary", "Secret", "Mythic" },
	Default = _G.Settings.WebhookSettings.MinimumRarity,
	Tooltip = "Minimum rarity to send webhook notifications for",
	Callback = function(Value)
		_G.Settings.WebhookSettings.MinimumRarity = Value
	end,
})

WebhookBox:AddToggle("NotifyLegendary", {
	Text = "Notify for Legendary",
	Default = _G.Settings.WebhookSettings.NotifyLegendary,
	Tooltip = "Send webhook notifications for Legendary pets",
	Callback = function(Value)
		_G.Settings.WebhookSettings.NotifyLegendary = Value
	end,
})

WebhookBox:AddToggle("NotifySecret", {
	Text = "Notify for Secret",
	Default = _G.Settings.WebhookSettings.NotifySecret,
	Tooltip = "Send webhook notifications for Secret pets",
	Callback = function(Value)
		_G.Settings.WebhookSettings.NotifySecret = Value
	end,
})

WebhookBox:AddToggle("NotifyMythic", {
	Text = "Notify for Mythic",
	Default = _G.Settings.WebhookSettings.NotifyMythic,
	Tooltip = "Send webhook notifications for Mythic pets",
	Callback = function(Value)
		_G.Settings.WebhookSettings.NotifyMythic = Value
	end,
})

WebhookBox:AddToggle("NotifyShiny", {
	Text = "Notify for Shiny",
	Default = _G.Settings.WebhookSettings.NotifyShiny,
	Tooltip = "Send webhook notifications for Shiny pets",
	Callback = function(Value)
		_G.Settings.WebhookSettings.NotifyShiny = Value
	end,
})

WebhookBox:AddToggle("PingEveryone", {
	Text = "Ping @everyone",
	Default = _G.Settings.WebhookSettings.PingEveryone,
	Tooltip = "Ping everyone in the Discord server when a pet is hatched",
	Callback = function(Value)
		_G.Settings.WebhookSettings.PingEveryone = Value
	end,
})

WebhookBox:AddToggle("ModernStyle", {
	Text = "Modern Webhook Style",
	Default = _G.Settings.WebhookSettings.ModernStyle,
	Tooltip = "Use a cleaner, more modern webhook style",
	Callback = function(Value)
		_G.Settings.WebhookSettings.ModernStyle = Value
	end,
})

Library.ToggleKeybind = Options.MenuKeybind

SaveManager:SetLibrary(Library)
ThemeManager:SetLibrary(Library)
SaveManager:IgnoreThemeSettings()
SaveManager:SetIgnoreIndexes({ "ToggleESP", "ItemList_ResetPos", "PetSelection" })
ThemeManager:SetFolder("0ne Hub")
SaveManager:SetFolder("0ne Hub/BubbleInfinity")
SaveManager:BuildConfigSection(SettingsTab)
ThemeManager:ApplyToTab(SettingsTab)

SaveManager:LoadAutoloadConfig()

Library:Notify({
	Title = "0ne Hub Loaded",
	Description = "Press Right-Ctrl to toggle UI",
	Time = 5,
})

-- Added Auto Competition UI
local AutoCompBox = AutomationTab:AddRightGroupbox("Auto Competition")

local AutoCompStatusLabel = AutoCompBox:AddLabel({ Text = "Status: Idle", DoesWrap = true })

task.spawn(function()
	while true do
		AutoCompStatusLabel:SetText("Status: " .. (_G.Settings.AutoCompetitionSettings._Status or "Idle"))
		task.wait(0.1)
	end
end)

local AutoCompToggle = AutoCompBox:AddToggle("AutoCompEnabled", {
	Text = "Enable Auto Competition",
	Default = _G.Settings.AutoCompetitionSettings.Enabled,
	Tooltip = "Automatically completes competitive quests",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.Enabled = Value
		if Value then
			AutoCompetition:Start()
		else
			AutoCompetition:Stop()
		end
	end,
})

local AutoRerollToggle = AutoCompBox:AddToggle("AutoCompAutoReroll", {
	Text = "Auto Reroll Quests",
	Default = _G.Settings.AutoCompetitionSettings.AutoReroll,
	Tooltip = "Automatically rerolls undesirable quests in slots 3 & 4",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.AutoReroll = Value
	end,
})

local RerollMythicToggle = AutoCompBox:AddToggle("AutoCompRerollMythic", {
	Text = "Reroll Mythic Hatch",
	Default = _G.Settings.AutoCompetitionSettings.RerollMythic,
	Tooltip = "Reroll quests requiring Mythic pet hatches",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.RerollMythic = Value
	end,
})

local RerollNonHatchToggle = AutoCompBox:AddToggle("AutoCompRerollNonHatch", {
	Text = "Reroll Non-Hatch",
	Default = _G.Settings.AutoCompetitionSettings.RerollNonHatch,
	Tooltip = "Reroll quests that are not hatch-related",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.RerollNonHatch = Value
	end,
})

local RerollThresholdSlider = AutoCompBox:AddSlider("AutoCompRerollThreshold", {
	Text = "Reroll Req. Threshold",
	Default = _G.Settings.AutoCompetitionSettings.RerollThreshold,
	Min = 100,
	Max = 2000,
	Rounding = 0,
	Compact = false,
	Tooltip = "Reroll specific egg hatch quests if requirement exceeds this",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.RerollThreshold = Value
	end,
})

-- Ideally, populate dynamically, but keep it simple for now.
local commonEggs = {"Common Egg", "Rare Egg", "Epic Egg", "Legendary Egg", "Spikey Egg", "Infinity Egg", "Rift Egg"}
local RarityEggDropdown = AutoCompBox:AddDropdown("AutoCompRarityEgg", {
	Text = "Rarity Hatch Egg",
	Default = _G.Settings.AutoCompetitionSettings.EggPreferences.RarityHatch,
	Values = commonEggs,
	Tooltip = "Egg to use for Rarity hatch quests",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.EggPreferences.RarityHatch = Value
	end,
})

local GenericEggDropdown = AutoCompBox:AddDropdown("AutoCompGenericEgg", {
	Text = "Generic Hatch Egg",
	Default = _G.Settings.AutoCompetitionSettings.EggPreferences.GenericHatch,
	Values = commonEggs,
	Tooltip = "Egg to use for Generic hatch quests",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.EggPreferences.GenericHatch = Value
	end,
})

local ShinyEggDropdown = AutoCompBox:AddDropdown("AutoCompShinyEgg", {
	Text = "Shiny Hatch Egg",
	Default = _G.Settings.AutoCompetitionSettings.EggPreferences.ShinyHatch,
	Values = commonEggs,
	Tooltip = "Egg to use for Shiny hatch quests",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.EggPreferences.ShinyHatch = Value
	end,
})

local PriorityOrderDropdown = AutoCompBox:AddDropdown("AutoCompPriorityOrder", {
	Text = "Quest Priority Order",
	Values = { "RarityHatch", "SpecificHatch", "GenericHatch", "ShinyHatch" },
	Multi = true,
	Default = _G.Settings.AutoCompetitionSettings.PriorityOrder,
	Tooltip = "Order of quest types to prioritize. Select multiple in desired order.",
	Callback = function(Value) -- Value is a table { ["ItemName"] = true/false }
		local orderedPriority = {}
		-- Iterate through the canonical order to preserve it
		for _, item in ipairs({ "RarityHatch", "SpecificHatch", "GenericHatch", "ShinyHatch" }) do
			if Value[item] then
				table.insert(orderedPriority, item)
			end
		end
		_G.Settings.AutoCompetitionSettings.PriorityOrder = orderedPriority
	end,
})

local DelaySlider = AutoCompBox:AddSlider("AutoCompDelay", {
	Text = "Process Delay (s)",
	Default = _G.Settings.AutoCompetitionSettings.Delay,
	Min = 0.1,
	Max = 10,
	Rounding = 1,
	Compact = false,
	Tooltip = "Delay between quest processing attempts",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.Delay = Value
	end,
})

local FindEggMethodDropdown = AutoCompBox:AddDropdown("AutoCompFindEggMethod", {
	Text = "Find Egg Method",
	Values = { "Rendered", "Worlds" },
	Default = _G.Settings.AutoCompetitionSettings.FindEggMethod,
	Tooltip = "How to find the egg model (Rendered is faster but potentially less reliable, Worlds is slower but more reliable)",
	Callback = function(Value)
		_G.Settings.AutoCompetitionSettings.FindEggMethod = Value
	end,
})
